{"timestamp": "2025-06-12T00:23:48.369Z", "pagesAnalyzed": [7, 66, 83, 115, 152], "totalPages": 258, "columns": [{"name": "Company_Name", "description": "Name of the business or company", "dataType": "string", "required": true, "examples": ["Fenix Seeds SRL", "Floritec", "Gartenbau Silber Gbr"]}, {"name": "Address_Line_1", "description": "First line of the business address", "dataType": "string", "required": false, "examples": ["251 Floris", "Gartengestaltung", "45-47 Rue d'Avignon"]}, {"name": "Address_Line_2", "description": "Second line of the business address (if applicable)", "dataType": "string", "required": false, "examples": ["", ""]}, {"name": "City", "description": "City where the business is located", "dataType": "string", "required": true, "examples": ["Heemstede", "<PERSON><PERSON>", "Avignon"]}, {"name": "State_Province", "description": "State or province where the business is located", "dataType": "string", "required": true, "examples": ["North Holland", "", "Rhone"]}, {"name": "Country", "description": "Country where the business is located", "dataType": "string", "required": true, "examples": ["Netherlands", "Germany", "France"]}, {"name": "Postal_Code", "description": "Postal code of the business address", "dataType": "string", "required": false, "examples": ["2151", "25837", "84130"]}, {"name": "Phone", "description": "Main phone number of the business", "dataType": "phone", "required": false, "examples": ["+**************", "+**************", "+33 4 90 240 240"]}, {"name": "Fax", "description": "Fax number of the business", "dataType": "phone", "required": false, "examples": ["+31 20 307 083", "+49 152 292 83", "+33 4 90 240 240"]}, {"name": "Email", "description": "Main email address of the business", "dataType": "email", "required": false, "examples": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, {"name": "Website", "description": "Website URL of the business", "dataType": "url", "required": false, "examples": ["www.fenixseeds.com", "www.floritec.eu", "www.gartenbau-silbergbr.de"]}, {"name": "Twitter/X", "description": "Twitter/X handle or URL", "dataType": "url", "required": false, "examples": ["@fenixseeds", null, null]}, {"name": "Facebook", "description": "Facebook page URL or handle", "dataType": "url", "required": false, "examples": [null, null, null]}, {"name": "LinkedIn", "description": "LinkedIn company or personal profile URL", "dataType": "url", "required": false, "examples": [null, null, null]}, {"name": "Instagram", "description": "Instagram handle or URL", "dataType": "url", "required": false, "examples": [null, null, null]}, {"name": "YouTube", "description": "YouTube channel URL", "dataType": "url", "required": false, "examples": [null, null, null]}, {"name": "Other_Social_Media", "description": "Other social media accounts not covered by specific columns (TikTok, Pinterest, etc.)", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "Founded", "description": "Year the business was founded", "dataType": "number", "required": false, "examples": ["1969", "2007", "1993"]}, {"name": "Years_in_Business", "description": "Number of years the business has been operating", "dataType": "number", "required": false, "examples": [null, null, null]}, {"name": "Staff", "description": "Number of employees", "dataType": "number", "required": false, "examples": [null, null, null]}, {"name": "Contact_Person", "description": "Name of the contact person at the business", "dataType": "string", "required": false, "examples": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, {"name": "Additional_Contact_Information", "description": "Additional contact details like phone, fax, and email", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "Business_Register_Number", "description": "Business registration number", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "VAT_Number", "description": "VAT number", "dataType": "string", "required": false, "examples": ["NL8520.219.75", null, null]}, {"name": "Brand", "description": "Brand name associated with the company", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "Products", "description": "List of products offered by the business", "dataType": "string", "required": false, "examples": ["seeds", "chrysanthemum cutflower and pot plants", "ornamental plants"]}, {"name": "Subsidiaries", "description": "List of subsidiaries (if any)", "dataType": "string", "required": false, "examples": ["Floricultura Pacific, Floricultura Orchidee", null, null]}, {"name": "Specialties", "description": "Specific services or products offered by the business", "dataType": "string", "required": false, "examples": ["Breeder", "Top quality in ericaceous young plants", null]}, {"name": "Category", "description": "Category or industry the business belongs to", "dataType": "string", "required": true, "examples": ["Breeders", "Greenhouse Technique", "Greenhouse Supplies"]}], "confidence": 1, "notes": "Additive analysis based on 5 random pages from 258 total pages. Enhanced existing 29 columns."}