{"timestamp": "2025-06-12T00:18:28.683Z", "pagesAnalyzed": [98, 116, 168, 243, 250], "totalPages": 258, "columns": [{"name": "Company_Name", "description": "Name of the business or company", "dataType": "string", "required": true, "examples": ["Unitec S.p.A.", "SAIJAB, Jordan", "Universal Irrigation Pipe Manufacturing"]}, {"name": "Address_Line_1", "description": "First line of the business address", "dataType": "string", "required": false, "examples": ["Via Provinciale Colognola", "P.O. Box 249", "1956 Setterington Drive"]}, {"name": "Address_Line_2", "description": "Second line of the business address (if applicable)", "dataType": "string", "required": false, "examples": ["Edificio EDGERTON", "Industrial Estate", ""]}, {"name": "City", "description": "City where the business is located", "dataType": "string", "required": true, "examples": ["Modena", "Amman", "Kingsville"]}, {"name": "State_Province", "description": "State or province where the business is located", "dataType": "string", "required": true, "examples": ["Emilia-Romagna", null, "NY"]}, {"name": "Country", "description": "Country where the business is located", "dataType": "string", "required": true, "examples": ["Italy", "Jordan", "USA"]}, {"name": "Postal_Code", "description": "Postal code of the business address", "dataType": "string", "required": false, "examples": ["41053", null, "10925"]}, {"name": "Phone", "description": "Main phone number of the business", "dataType": "phone", "required": false, "examples": ["+39 059 88 6137", "+962 6 480 251", "****** 326 1333"]}, {"name": "Fax", "description": "Fax number of the business", "dataType": "phone", "required": false, "examples": ["+39 059 88 6137", null, "****** 326 1333"]}, {"name": "Email", "description": "Main email address of the business", "dataType": "email", "required": false, "examples": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, {"name": "Website", "description": "Website URL of the business", "dataType": "url", "required": false, "examples": ["www.unitec-group.com", "www.dip.co.jo", "www.universal-irrigation.com"]}, {"name": "Twitter/X", "description": "Twitter/X handle or URL", "dataType": "url", "required": false, "examples": [null, null, null]}, {"name": "Facebook", "description": "Facebook page URL or handle", "dataType": "url", "required": false, "examples": [null, null, null]}, {"name": "LinkedIn", "description": "LinkedIn company or personal profile URL", "dataType": "url", "required": false, "examples": [null, null, null]}, {"name": "Instagram", "description": "Instagram handle or URL", "dataType": "url", "required": false, "examples": [null, null, null]}, {"name": "YouTube", "description": "YouTube channel URL", "dataType": "url", "required": false, "examples": [null, null, null]}, {"name": "Other_Social_Media", "description": "Other social media platforms (TikTok, Pinterest, etc.)", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "Founded", "description": "Year the business was founded", "dataType": "number", "required": false, "examples": ["1924", "1986", "1956"]}, {"name": "Subsidiaries", "description": "List of subsidiaries (if any)", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "Staff", "description": "Number of employees", "dataType": "number", "required": false, "examples": [null, null, null]}, {"name": "Category", "description": "Category or industry the business belongs to", "dataType": "string", "required": true, "examples": ["Greenhouse Technique", "Greenhouse Supplies", "Greenhouse Parts | Screening | Ventilation"]}, {"name": "Specialties", "description": "Specific services or products offered by the business", "dataType": "string", "required": false, "examples": ["technologies for the processing and quality sorting of fruit and vegetables", "parasiticides for the control of aphids", ""]}, {"name": "Contact_Person", "description": "Name of the contact person at the business", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "Business_Register_Number", "description": "Business registration number", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "VAT_Number", "description": "VAT number", "dataType": "string", "required": false, "examples": ["NL004529704B01", null, null]}, {"name": "Internet", "description": "Website or other online presence", "dataType": "url", "required": false, "examples": ["www.unitec-group.com", null, null]}, {"name": "Brand", "description": "Brand name associated with the company", "dataType": "string", "required": false, "examples": ["Unitec", "MondiCrop", "Valk Horst Systems"]}, {"name": "Years_in_Business", "description": "Number of years the business has been operating", "dataType": "number", "required": false, "examples": ["75", "20", "60"]}, {"name": "Contacts", "description": "Additional contact information", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "Products", "description": "List of products offered by the business", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "Additional_Contact_Information", "description": "Additional contact details beyond phone, fax, and email", "dataType": "string", "required": false, "examples": [null, null, null]}, {"name": "Other_Social_Media", "description": "Other social media accounts not covered by specific columns (TikTok, Pinterest, etc.)", "dataType": "string", "required": false, "examples": ["TikTok: @company", "Pinterest: pinterest.com/company", "WhatsApp: +**********"]}], "confidence": 1, "notes": "Additive analysis based on 5 random pages from 258 total pages. Enhanced existing 31 columns."}