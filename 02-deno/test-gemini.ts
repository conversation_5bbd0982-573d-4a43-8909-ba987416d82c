#!/usr/bin/env -S deno run --allow-all

import { GoogleGenerativeAI } from '@google/generative-ai';
import { load } from '@std/dotenv/mod.ts';
import { encodeBase64 } from '@std/encoding/base64.ts';

async function testGeminiConnection() {
  try {
    console.log('🔍 Testing Gemini API connection...');

    // Load environment variables
    const env = await load();
    const apiKey = env.GEMINI_API_KEY || Deno.env.get('GEMINI_API_KEY');

    if (!apiKey) {
      throw new Error('GEMINI_API_KEY not found');
    }

    console.log('✅ API key found');

    // Initialize Gemini
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({
      model: 'gemini-1.5-flash',
      generationConfig: {
        temperature: 0.1,
        maxOutputTokens: 1000,
      }
    });

    console.log('✅ Model initialized');

    // Test with a simple text prompt
    console.log('🔄 Testing simple text generation...');
    const result = await model.generateContent('Hello, please respond with "API connection successful"');
    const response = result.response;
    const text = response.text();

    console.log('✅ Response received:', text);

    // Test with an actual converted PDF image
    console.log('🔄 Testing image analysis with real PDF page...');

    // Check if we have any converted images
    const tempDir = './temp';
    let testImagePath = '';

    try {
      for await (const dirEntry of Deno.readDir(tempDir)) {
        if (dirEntry.isFile && dirEntry.name.endsWith('.jpg')) {
          testImagePath = `${tempDir}/${dirEntry.name}`;
          break;
        }
      }
    } catch {
      console.log('⚠️  No converted images found in temp directory. Skipping image test.');
      console.log('🎉 Text generation test passed! Gemini API is working correctly.');
      return;
    }

    if (!testImagePath) {
      console.log('⚠️  No JPG files found in temp directory. Skipping image test.');
      console.log('🎉 Text generation test passed! Gemini API is working correctly.');
      return;
    }

    console.log(`📸 Using test image: ${testImagePath}`);

    // Read the actual image file
    const imageData = await Deno.readFile(testImagePath);
    const base64Image = encodeBase64(imageData);

    const imageResult = await model.generateContent([
      'Describe this image briefly',
      {
        inlineData: {
          data: base64Image,
          mimeType: 'image/jpeg',
        },
      },
    ]);

    const imageResponse = imageResult.response;
    const imageText = imageResponse.text();

    console.log('✅ Image analysis response:', imageText);
    console.log('🎉 All tests passed! Gemini API is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', (error as Error).message);

    if ((error as Error).message.includes('BadRecordMac')) {
      console.log('\n💡 Troubleshooting suggestions:');
      console.log('1. Check your internet connection');
      console.log('2. Try using a VPN if behind a corporate firewall');
      console.log('3. Check if your network blocks Google APIs');
      console.log('4. Try running the command again (temporary network issue)');
    }

    Deno.exit(1);
  }
}

if (import.meta.main) {
  await testGeminiConnection();
}
