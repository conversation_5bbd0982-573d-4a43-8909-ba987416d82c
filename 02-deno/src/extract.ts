import { join } from '@std/path/mod.ts';
import { Config } from './config.ts';
import { PDFService } from './services/pdf.ts';
import { GeminiService } from './services/gemini.ts';
import { AnalyzeUtility } from './analyze.ts';
import { logger } from './utils/logger.ts';
import { FileUtils } from './utils/file.ts';
import type { AnalysisResult, ExtractionResult, BusinessListing, ExtractionOptions, CategoryMapping } from './types.ts';

export class ExtractUtility {
  private config: Config;
  private pdfService: PDFService;
  private geminiService: GeminiService;
  private analyzeUtility: AnalyzeUtility;

  constructor() {
    this.config = {} as Config;
    this.pdfService = {} as PDFService;
    this.geminiService = {} as GeminiService;
    this.analyzeUtility = new AnalyzeUtility();
  }

  async init(): Promise<void> {
    await logger.init();
    await logger.info('Initializing Extract Utility');

    // Load configuration
    this.config = await Config.getInstance();
    await this.config.validateConfig();

    const geminiConfig = this.config.getGeminiConfig();
    const processingConfig = this.config.getProcessingConfig();

    // Initialize services
    this.pdfService = new PDFService(processingConfig.pdfPath, processingConfig.tempDir);
    this.geminiService = new GeminiService(geminiConfig);
    await this.analyzeUtility.init();

    await this.pdfService.init();

    await logger.success('Extract Utility initialized successfully');
  }

  async extract(analysisResult?: AnalysisResult): Promise<ExtractionResult[]> {
    return await this.extractWithOptions(analysisResult);
  }

  async extractWithOptions(analysisResult?: AnalysisResult, options?: ExtractionOptions): Promise<ExtractionResult[]> {
    try {
      await logger.info('Starting data extraction process');

      // Get or load analysis result
      let analysis = analysisResult;
      if (!analysis) {
        const latestAnalysis = await this.analyzeUtility.getLatestAnalysis();
        if (!latestAnalysis) {
          throw new Error('No analysis result found. Please run analysis first.');
        }
        analysis = latestAnalysis;
      }

      await logger.info(`Using analysis with ${analysis.columns.length} columns`);

      const processingConfig = this.config.getProcessingConfig();
      await this.pdfService.getTotalPages(); // Ensure PDF is initialized

      // Determine which pages to process
      let pagesToProcess: number[];
      if (options?.pageRange) {
        const { startPage, endPage } = options.pageRange;
        pagesToProcess = [];
        for (let i = startPage; i <= endPage; i++) {
          pagesToProcess.push(i);
        }
        await logger.info(`Processing page range: ${startPage}-${endPage} (${pagesToProcess.length} pages)`);
      } else {
        pagesToProcess = this.pdfService.getAllPageNumbers();
        await logger.info(`Processing all pages: ${pagesToProcess.length} pages`);
      }

      // Process pages in batches
      const batchSize = options?.batchSize || processingConfig.maxPagesPerBatch;
      const results: ExtractionResult[] = [];

      for (let i = 0; i < pagesToProcess.length; i += batchSize) {
        const batch = pagesToProcess.slice(i, i + batchSize);
        await logger.progress(`Processing batch ${Math.floor(i / batchSize) + 1}`, i + batch.length, pagesToProcess.length);

        const batchResults = await this.processBatch(batch, analysis, options);
        results.push(...batchResults);

        // Save intermediate results (cumulative if specified)
        await this.saveBatchResults(batchResults, options?.cumulativeResults);

        // Small delay between batches to avoid rate limiting
        if (i + batchSize < pagesToProcess.length) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      await logger.success(`Data extraction completed. Processed ${results.length} pages`);
      return results;

    } catch (error) {
      await logger.error('Data extraction failed', error);
      throw error;
    } finally {
      await this.pdfService.cleanup();
    }
  }

  private async processBatch(
    pageNumbers: number[],
    analysis: AnalysisResult,
    _options?: ExtractionOptions
  ): Promise<ExtractionResult[]> {
    const results: ExtractionResult[] = [];

    for (const pageNumber of pageNumbers) {
      const result = await this.processPageWithRetry(pageNumber, analysis);
      results.push(result);
    }

    return results;
  }

  private async processPageWithRetry(
    pageNumber: number,
    analysis: AnalysisResult,
    maxRetries: number = 3
  ): Promise<ExtractionResult> {
    // Load existing extraction result if available
    const existingResult = await this.loadExistingPageResult(pageNumber);
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const startTime = Date.now();

        if (attempt > 1) {
          await logger.info(`🔄 Retry attempt ${attempt}/${maxRetries} for page ${pageNumber}`);
          console.log(`🔄 Retry attempt ${attempt}/${maxRetries} for page ${pageNumber}`);
        }

        // Convert page to image
        const pageInfo = await this.pdfService.convertPageToImage(pageNumber);

        // Extract data from the page (includes both listings and category detection)
        const extractionResult = await this.geminiService.extractDataFromImage(
          pageInfo.imagePath,
          analysis.columns,
          pageNumber
        );

        // Clean and validate the data
        const cleanedListings = this.geminiService.validateAndCleanData(extractionResult.listings);

        // Save category information if detected
        if (extractionResult.category) {
          await this.savePageCategory(pageNumber, extractionResult.category);
        }

        // Handle multi-page entries
        const processedListings = await this.handleMultiPageEntries(cleanedListings, pageNumber);

        // Merge with existing data if available
        const finalListings = existingResult
          ? this.mergeWithExistingData(existingResult.listings, processedListings)
          : processedListings;

        const processingTime = Date.now() - startTime;

        const result: ExtractionResult = {
          timestamp: new Date().toISOString(),
          pageNumber,
          totalEntries: finalListings.length,
          listings: finalListings,
          processingTime,
        };

        await logger.debug(`Page ${pageNumber} processed: ${processedListings.length} entries in ${processingTime}ms`);

        if (attempt > 1) {
          await logger.success(`✅ Page ${pageNumber} succeeded on attempt ${attempt}`);
          console.log(`✅ Page ${pageNumber} succeeded on attempt ${attempt}`);
        }

        return result;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        const errorMessage = `❌ Attempt ${attempt}/${maxRetries} failed for page ${pageNumber}: ${lastError.message}`;
        await logger.error(errorMessage, lastError);
        console.error(errorMessage);

        if (lastError.stack) {
          console.error(`Stack trace: ${lastError.stack}`);
        }

        // If this is not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          const waitTime = attempt * 1000; // Progressive backoff: 1s, 2s, 3s
          await logger.info(`⏳ Waiting ${waitTime}ms before retry...`);
          console.log(`⏳ Waiting ${waitTime}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }

    // All retries failed, create error result
    const finalErrorMessage = `Failed to process page ${pageNumber} after ${maxRetries} attempts. Final error: ${lastError?.message}`;
    await logger.error(finalErrorMessage, lastError);
    console.error(`🚫 ${finalErrorMessage}`);

    const errorResult: ExtractionResult = {
      timestamp: new Date().toISOString(),
      pageNumber,
      totalEntries: 0,
      listings: [],
      processingTime: 0,
      errors: [finalErrorMessage],
    };

    return errorResult;
  }

  private async loadExistingPageResult(pageNumber: number): Promise<ExtractionResult | null> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const extractedDir = join(processingConfig.outputDir, 'extracted');
      const filename = `page_${pageNumber.toString().padStart(3, '0')}.json`;
      const filePath = join(extractedDir, filename);

      if (await FileUtils.fileExists(filePath)) {
        return await FileUtils.readJsonFile<ExtractionResult>(filePath);
      }

      return null;
    } catch (error) {
      await logger.warn(`Failed to load existing result for page ${pageNumber}`, error);
      return null;
    }
  }



  private async loadCategoryMapping(): Promise<CategoryMapping> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const categoryPath = join(processingConfig.outputDir, 'category.json');

      if (await FileUtils.fileExists(categoryPath)) {
        return await FileUtils.readJsonFile<CategoryMapping>(categoryPath);
      }

      return {};
    } catch (error) {
      await logger.warn('Failed to load category mapping', error);
      return {};
    }
  }

  private async saveCategoryMapping(categoryMapping: CategoryMapping): Promise<void> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const categoryPath = join(processingConfig.outputDir, 'category.json');

      await FileUtils.writeJsonFile(categoryPath, categoryMapping);
    } catch (error) {
      await logger.error('Failed to save category mapping', error);
    }
  }

  private async savePageCategory(pageNumber: number, categoryInfo: { categoryName: string; color?: string; description?: string; confidence: number }): Promise<void> {
    try {
      // Load existing category mapping
      const categoryMapping = await this.loadCategoryMapping();

      // Update category mapping
      categoryMapping[pageNumber] = categoryInfo;

      // Save updated category mapping
      await this.saveCategoryMapping(categoryMapping);

      await logger.debug(`Detected category "${categoryInfo.categoryName}" for page ${pageNumber}`);
    } catch (error) {
      await logger.warn(`Failed to save category for page ${pageNumber}`, error);
    }
  }

  private mergeWithExistingData(existingListings: BusinessListing[], newListings: BusinessListing[]): BusinessListing[] {
    const mergedListings: BusinessListing[] = [];

    // Create a map of existing listings by entry number for quick lookup
    const existingMap = new Map<number, BusinessListing>();
    existingListings.forEach(listing => {
      existingMap.set(listing.entryNumber, listing);
    });

    // Process new listings
    for (const newListing of newListings) {
      const existing = existingMap.get(newListing.entryNumber);

      if (existing) {
        // Merge the listings, preferring high-confidence new data
        const merged = this.mergeBusinessListings(existing, newListing);
        mergedListings.push(merged);
        existingMap.delete(newListing.entryNumber); // Mark as processed
      } else {
        // New listing not in existing data
        mergedListings.push(newListing);
      }
    }

    // Add any remaining existing listings that weren't updated
    existingMap.forEach(listing => {
      mergedListings.push(listing);
    });

    // Sort by entry number
    mergedListings.sort((a, b) => a.entryNumber - b.entryNumber);

    return mergedListings;
  }

  private mergeBusinessListings(existing: BusinessListing, newListing: BusinessListing): BusinessListing {
    const merged: BusinessListing = {
      ...existing,
      data: { ...existing.data }
    };

    // Update timestamp and processing info
    merged.pageNumber = newListing.pageNumber;

    // Merge data fields, preferring new data if it has higher confidence or fills empty fields
    for (const [key, newValue] of Object.entries(newListing.data)) {
      const existingValue = existing.data[key];

      // Use new value if:
      // 1. Existing value is empty/null and new value is not
      // 2. New listing has higher confidence and new value is not empty
      // 3. New value is significantly longer (likely more complete)
      if (this.shouldUseNewValue(existingValue, newValue, existing.confidence, newListing.confidence)) {
        merged.data[key] = newValue;
      }
    }

    // Update confidence to the higher of the two
    merged.confidence = Math.max(existing.confidence, newListing.confidence);

    // Preserve important flags
    if (existing.spanMultiplePages || newListing.spanMultiplePages) {
      merged.spanMultiplePages = true;
    }
    const continuedFromPage = existing.continuedFromPage ?? newListing.continuedFromPage;
    if (continuedFromPage !== undefined) {
      merged.continuedFromPage = continuedFromPage;
    }
    const continuesOnPage = existing.continuesOnPage ?? newListing.continuesOnPage;
    if (continuesOnPage !== undefined) {
      merged.continuesOnPage = continuesOnPage;
    }

    // Update geographic and social media data
    const bestCountry = this.selectBestValue(existing.country, newListing.country);
    if (bestCountry) merged.country = bestCountry;

    const bestCity = this.selectBestValue(existing.city, newListing.city);
    if (bestCity) merged.city = bestCity;

    const bestState = this.selectBestValue(existing.stateProvince, newListing.stateProvince);
    if (bestState) merged.stateProvince = bestState;

    const bestTwitter = this.selectBestValue(existing.twitterX, newListing.twitterX);
    if (bestTwitter) merged.twitterX = bestTwitter;

    const bestFacebook = this.selectBestValue(existing.facebook, newListing.facebook);
    if (bestFacebook) merged.facebook = bestFacebook;

    const bestLinkedIn = this.selectBestValue(existing.linkedin, newListing.linkedin);
    if (bestLinkedIn) merged.linkedin = bestLinkedIn;

    const bestInstagram = this.selectBestValue(existing.instagram, newListing.instagram);
    if (bestInstagram) merged.instagram = bestInstagram;

    const bestYouTube = this.selectBestValue(existing.youtube, newListing.youtube);
    if (bestYouTube) merged.youtube = bestYouTube;

    const bestOtherSocial = this.selectBestValue(existing.otherSocialMedia, newListing.otherSocialMedia);
    if (bestOtherSocial) merged.otherSocialMedia = bestOtherSocial;

    return merged;
  }

  private shouldUseNewValue(
    existingValue: string | number | null | undefined,
    newValue: string | number | null | undefined,
    existingConfidence: number,
    newConfidence: number
  ): boolean {
    // If existing value is empty/null, use new value if it's not empty
    if (!existingValue && newValue) {
      return true;
    }

    // If new value is empty, keep existing
    if (!newValue) {
      return false;
    }

    // If new confidence is significantly higher (>0.1 difference), use new value
    if (newConfidence - existingConfidence > 0.1) {
      return true;
    }

    // If new value is significantly longer (likely more complete), use it
    if (typeof existingValue === 'string' && typeof newValue === 'string') {
      return newValue.length > existingValue.length * 1.5;
    }

    return false;
  }

  private selectBestValue<T>(existing: T | undefined, newValue: T | undefined): T | undefined {
    if (!existing && newValue) return newValue;
    if (!newValue) return existing;

    // If both exist, prefer the longer/more complete one for strings
    if (typeof existing === 'string' && typeof newValue === 'string') {
      return newValue.length > existing.length ? newValue : existing;
    }

    return existing; // Default to existing value
  }

  private async handleMultiPageEntries(
    listings: BusinessListing[],
    pageNumber: number
  ): Promise<BusinessListing[]> {
    const processedListings: BusinessListing[] = [];

    // Load previous page result to check for continuation
    const previousPageResult = pageNumber > 1 ? await this.loadExistingPageResult(pageNumber - 1) : null;

    for (const listing of listings) {
      // Ensure page number is set correctly
      listing.pageNumber = pageNumber;

      // Extract geographic data from address fields if not already present
      this.extractGeographicData(listing);

      // Separate social media accounts into individual columns
      this.separateSocialMediaAccounts(listing);

      // Check if this listing is a continuation from the previous page
      const isLikelyContinuation = this.isLikelyContinuation(listing);

      if (isLikelyContinuation && previousPageResult) {
        // This listing appears to be a continuation - check if we should skip it
        const lastListingFromPreviousPage = previousPageResult.listings[previousPageResult.listings.length - 1];

        if (lastListingFromPreviousPage && lastListingFromPreviousPage.spanMultiplePages) {
          // Skip this listing as it's a duplicate of one that started on the previous page
          await logger.debug(`Skipping duplicate listing on page ${pageNumber} (continuation from page ${pageNumber - 1})`);
          continue;
        }
      }

      // Check if this listing appears to be cut off (spans to next page)
      const isLikelyIncomplete = this.isLikelyIncomplete(listing);

      if (isLikelyIncomplete) {
        listing.spanMultiplePages = true;
        listing.continuesOnPage = pageNumber + 1;
        await logger.debug(`Listing "${listing.data.Company_Name || 'Unknown'}" on page ${pageNumber} appears to continue on next page`);
      }

      processedListings.push(listing);
    }

    return processedListings;
  }

  private isLikelyContinuation(listing: BusinessListing): boolean {
    // Check if this listing is likely a continuation from the previous page
    const companyName = listing.data.Company_Name || listing.data.company_name || listing.data.business_name || listing.data.name;

    // If there's no company name or it's very short, it might be a continuation
    if (!companyName || (typeof companyName === 'string' && companyName.trim().length < 3)) {
      return true;
    }

    // If confidence is very low, it might be a partial listing
    if (listing.confidence < 0.5) {
      return true;
    }

    // Check if the listing has very few fields filled
    const filledFields = Object.values(listing.data).filter(value => value !== null && value !== undefined && value !== '').length;
    if (filledFields < 3) {
      return true;
    }

    return false;
  }

  private isLikelyIncomplete(listing: BusinessListing): boolean {
    // Check if this listing appears to be cut off at the bottom of the page

    // If confidence is low, it might be incomplete
    if (listing.confidence < 0.7) {
      return true;
    }

    // Check for incomplete address information
    const hasAddress = listing.data.Address_Line_1 || listing.data.address || listing.data.company_address;
    const hasCity = listing.data.City || listing.data.city;
    const hasCountry = listing.data.Country || listing.data.country;

    // If we have partial address info but missing key components, it might be incomplete
    if (hasAddress && (!hasCity || !hasCountry)) {
      return true;
    }

    // Check for very short or truncated text that might indicate cutoff
    const companyName = listing.data.Company_Name || listing.data.company_name || listing.data.business_name || listing.data.name;
    if (typeof companyName === 'string' && companyName.endsWith('...')) {
      return true;
    }

    return false;
  }

  private extractGeographicData(listing: BusinessListing): void {
    // Extract country, state/province, and city from address fields
    const addressFields = [
      'address', 'company_address', 'mailing_address', 'main_address',
      'business_address', 'physical_address', 'location'
    ];

    for (const field of addressFields) {
      const value = listing.data[field];
      if (typeof value === 'string' && value.trim()) {
        const geoData = this.parseGeographicData(value);

        // Only set if not already present in the data
        if (geoData.country && !listing.data.Country && !listing.country) {
          listing.data.Country = geoData.country;
          listing.country = geoData.country;
        }
        if (geoData.state && !listing.data.State_Province) {
          listing.data.State_Province = geoData.state;
        }
        if (geoData.city && !listing.data.City) {
          listing.data.City = geoData.city;
        }

        // If we found geographic data, we can break
        if (geoData.country || geoData.state || geoData.city) {
          break;
        }
      }
    }
  }

  private parseGeographicData(address: string): { country?: string; state?: string; city?: string } {
    const result: { country?: string; state?: string; city?: string } = {};

    // Extract country
    const country = this.extractCountryFromAddress(address);
    if (country) {
      result.country = country;
    }

    // Extract state/province (common patterns)
    const statePatterns = [
      // US States
      /\b(Alabama|Alaska|Arizona|Arkansas|California|Colorado|Connecticut|Delaware|Florida|Georgia|Hawaii|Idaho|Illinois|Indiana|Iowa|Kansas|Kentucky|Louisiana|Maine|Maryland|Massachusetts|Michigan|Minnesota|Mississippi|Missouri|Montana|Nebraska|Nevada|New Hampshire|New Jersey|New Mexico|New York|North Carolina|North Dakota|Ohio|Oklahoma|Oregon|Pennsylvania|Rhode Island|South Carolina|South Dakota|Tennessee|Texas|Utah|Vermont|Virginia|Washington|West Virginia|Wisconsin|Wyoming)\b/i,
      /\b(AL|AK|AZ|AR|CA|CO|CT|DE|FL|GA|HI|ID|IL|IN|IA|KS|KY|LA|ME|MD|MA|MI|MN|MS|MO|MT|NE|NV|NH|NJ|NM|NY|NC|ND|OH|OK|OR|PA|RI|SC|SD|TN|TX|UT|VT|VA|WA|WV|WI|WY)\b/,
      // Canadian Provinces
      /\b(Ontario|Quebec|British Columbia|Alberta|Manitoba|Saskatchewan|Nova Scotia|New Brunswick|Newfoundland and Labrador|Prince Edward Island|Northwest Territories|Nunavut|Yukon)\b/i,
      /\b(ON|QC|BC|AB|MB|SK|NS|NB|NL|PE|NT|NU|YT)\b/,
      // Common international patterns
      /\b(Bavaria|Baden-Württemberg|Berlin|Brandenburg|Bremen|Hamburg|Hesse|Lower Saxony|Mecklenburg-Vorpommern|North Rhine-Westphalia|Rhineland-Palatinate|Saarland|Saxony|Saxony-Anhalt|Schleswig-Holstein|Thuringia)\b/i,
    ];

    for (const pattern of statePatterns) {
      const match = address.match(pattern);
      if (match && match[1]) {
        result.state = match[1];
        break;
      }
    }

    // Extract city (this is more complex, but we can try some patterns)
    // Look for patterns like "City, State" or "City State"
    const cityPatterns = [
      /([A-Za-z\s]+),\s*([A-Z]{2})\b/, // "City Name, ST"
      /([A-Za-z\s]+)\s+([A-Z]{2})\s+\d{5}/, // "City Name ST 12345"
      /^([A-Za-z\s]+),/, // "City Name," at start
    ];

    for (const pattern of cityPatterns) {
      const match = address.match(pattern);
      if (match && match[1]) {
        const cityCandidate = match[1].trim();
        // Basic validation - city should be reasonable length and not contain numbers
        if (cityCandidate.length >= 2 && cityCandidate.length <= 50 && !/\d/.test(cityCandidate)) {
          result.city = cityCandidate;
          break;
        }
      }
    }

    return result;
  }

  private separateSocialMediaAccounts(listing: BusinessListing): void {
    // Look for social media information in various fields
    const socialFields = [
      'social_media', 'social', 'social_accounts', 'social_media_accounts',
      'twitter', 'facebook', 'linkedin', 'instagram', 'youtube',
      'website', 'websites', 'urls', 'links'
    ];

    const socialData = {
      twitter: '',
      facebook: '',
      linkedin: '',
      instagram: '',
      youtube: '',
      other: [] as string[]
    };

    for (const field of socialFields) {
      const value = listing.data[field];
      if (typeof value === 'string' && value.trim()) {
        this.extractSocialMediaFromText(value, socialData);
      }
    }

    // Set the separated social media data
    if (socialData.twitter && !listing.data.Twitter_X) {
      listing.data.Twitter_X = socialData.twitter;
    }
    if (socialData.facebook && !listing.data.Facebook) {
      listing.data.Facebook = socialData.facebook;
    }
    if (socialData.linkedin && !listing.data.LinkedIn) {
      listing.data.LinkedIn = socialData.linkedin;
    }
    if (socialData.instagram && !listing.data.Instagram) {
      listing.data.Instagram = socialData.instagram;
    }
    if (socialData.youtube && !listing.data.YouTube) {
      listing.data.YouTube = socialData.youtube;
    }
    if (socialData.other.length > 0 && !listing.data.Other_Social_Media) {
      listing.data.Other_Social_Media = socialData.other.join('; ');
    }
  }

  private extractSocialMediaFromText(text: string, socialData: {
    twitter: string;
    facebook: string;
    linkedin: string;
    instagram: string;
    youtube: string;
    other: string[];
  }): void {
    // Twitter/X patterns
    const twitterPatterns = [
      /(?:https?:\/\/)?(?:www\.)?(?:twitter\.com|x\.com)\/([a-zA-Z0-9_]+)/gi,
      /@([a-zA-Z0-9_]+)(?:\s|$)/g,
      /twitter:\s*@?([a-zA-Z0-9_]+)/gi,
      /x\.com\/([a-zA-Z0-9_]+)/gi
    ];

    // Facebook patterns
    const facebookPatterns = [
      /(?:https?:\/\/)?(?:www\.)?facebook\.com\/([a-zA-Z0-9._-]+)/gi,
      /facebook:\s*([a-zA-Z0-9._-]+)/gi
    ];

    // LinkedIn patterns
    const linkedinPatterns = [
      /(?:https?:\/\/)?(?:www\.)?linkedin\.com\/(?:company|in)\/([a-zA-Z0-9._-]+)/gi,
      /linkedin:\s*([a-zA-Z0-9._-]+)/gi
    ];

    // Instagram patterns
    const instagramPatterns = [
      /(?:https?:\/\/)?(?:www\.)?instagram\.com\/([a-zA-Z0-9._]+)/gi,
      /instagram:\s*@?([a-zA-Z0-9._]+)/gi
    ];

    // YouTube patterns
    const youtubePatterns = [
      /(?:https?:\/\/)?(?:www\.)?youtube\.com\/(?:c\/|channel\/|@)([a-zA-Z0-9._-]+)/gi,
      /youtube:\s*([a-zA-Z0-9._-]+)/gi
    ];

    // Extract Twitter/X
    for (const pattern of twitterPatterns) {
      const matches = [...text.matchAll(pattern)];
      if (matches.length > 0 && matches[0] && matches[0][0] && !socialData.twitter) {
        socialData.twitter = matches[0][0];
        break;
      }
    }

    // Extract Facebook
    for (const pattern of facebookPatterns) {
      const matches = [...text.matchAll(pattern)];
      if (matches.length > 0 && matches[0] && matches[0][0] && !socialData.facebook) {
        socialData.facebook = matches[0][0];
        break;
      }
    }

    // Extract LinkedIn
    for (const pattern of linkedinPatterns) {
      const matches = [...text.matchAll(pattern)];
      if (matches.length > 0 && matches[0] && matches[0][0] && !socialData.linkedin) {
        socialData.linkedin = matches[0][0];
        break;
      }
    }

    // Extract Instagram
    for (const pattern of instagramPatterns) {
      const matches = [...text.matchAll(pattern)];
      if (matches.length > 0 && matches[0] && matches[0][0] && !socialData.instagram) {
        socialData.instagram = matches[0][0];
        break;
      }
    }

    // Extract YouTube
    for (const pattern of youtubePatterns) {
      const matches = [...text.matchAll(pattern)];
      if (matches.length > 0 && matches[0] && matches[0][0] && !socialData.youtube) {
        socialData.youtube = matches[0][0];
        break;
      }
    }

    // Extract other social media (TikTok, Pinterest, etc.)
    const otherPatterns = [
      /(?:https?:\/\/)?(?:www\.)?tiktok\.com\/([a-zA-Z0-9._@]+)/gi,
      /(?:https?:\/\/)?(?:www\.)?pinterest\.com\/([a-zA-Z0-9._-]+)/gi,
      /whatsapp:\s*([+\d\s()-]+)/gi,
      /telegram:\s*@?([a-zA-Z0-9._]+)/gi
    ];

    for (const pattern of otherPatterns) {
      const matches = [...text.matchAll(pattern)];
      for (const match of matches) {
        if (match && match[0] && !socialData.other.includes(match[0])) {
          socialData.other.push(match[0]);
        }
      }
    }
  }



  private extractCountryFromAddress(address: string): string | undefined {
    // Common country patterns and abbreviations
    const countryPatterns = [
      // Full country names
      /\b(United States|USA|US)\b/i,
      /\b(United Kingdom|UK|Britain)\b/i,
      /\b(Canada|CAN)\b/i,
      /\b(Germany|Deutschland|DE)\b/i,
      /\b(Netherlands|Holland|NL)\b/i,
      /\b(France|FR)\b/i,
      /\b(Italy|IT)\b/i,
      /\b(Spain|ES)\b/i,
      /\b(Australia|AU)\b/i,
      /\b(Japan|JP)\b/i,
      /\b(China|CN)\b/i,
      /\b(India|IN)\b/i,
      /\b(Brazil|BR)\b/i,
      /\b(Mexico|MX)\b/i,
      /\b(Belgium|BE)\b/i,
      /\b(Switzerland|CH)\b/i,
      /\b(Austria|AT)\b/i,
      /\b(Sweden|SE)\b/i,
      /\b(Norway|NO)\b/i,
      /\b(Denmark|DK)\b/i,
      /\b(Finland|FI)\b/i,
    ];

    const countryMappings: Record<string, string> = {
      'USA': 'United States',
      'US': 'United States',
      'UK': 'United Kingdom',
      'CAN': 'Canada',
      'DE': 'Germany',
      'NL': 'Netherlands',
      'FR': 'France',
      'IT': 'Italy',
      'ES': 'Spain',
      'AU': 'Australia',
      'JP': 'Japan',
      'CN': 'China',
      'IN': 'India',
      'BR': 'Brazil',
      'MX': 'Mexico',
      'BE': 'Belgium',
      'CH': 'Switzerland',
      'AT': 'Austria',
      'SE': 'Sweden',
      'NO': 'Norway',
      'DK': 'Denmark',
      'FI': 'Finland',
    };

    for (const pattern of countryPatterns) {
      const match = address.match(pattern);
      if (match && match[1]) {
        const found = match[1];
        return countryMappings[found.toUpperCase()] || found;
      }
    }

    return undefined;
  }

  private async saveBatchResults(results: ExtractionResult[], cumulative: boolean = false): Promise<void> {
    const processingConfig = this.config.getProcessingConfig();
    const extractedDir = join(processingConfig.outputDir, 'extracted');
    await FileUtils.ensureDirectoryExists(extractedDir);

    for (const result of results) {
      let filename: string;

      if (cumulative) {
        // Create timestamped files for cumulative results
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        filename = `page_${result.pageNumber.toString().padStart(3, '0')}_${timestamp}.json`;
      } else {
        // Use standard filename (overwrites previous results)
        filename = `page_${result.pageNumber.toString().padStart(3, '0')}.json`;
      }

      const filePath = join(extractedDir, filename);
      await FileUtils.writeJsonFile(filePath, result);
    }

    const resultType = cumulative ? 'cumulative' : 'standard';
    await logger.debug(`Saved ${results.length} ${resultType} extraction results`);
  }

  async getAllExtractionResults(): Promise<ExtractionResult[]> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const extractedDir = join(processingConfig.outputDir, 'extracted');

      if (!await FileUtils.fileExists(extractedDir)) {
        return [];
      }

      const files = await FileUtils.listFiles(extractedDir, '.json');
      const results: ExtractionResult[] = [];

      for (const file of files) {
        try {
          const result = await FileUtils.readJsonFile<ExtractionResult>(file);
          results.push(result);
        } catch (error) {
          await logger.warn(`Failed to load extraction result: ${file}`, error);
        }
      }

      // Sort by page number
      results.sort((a, b) => a.pageNumber - b.pageNumber);

      return results;
    } catch (error) {
      await logger.error('Failed to load extraction results', error);
      return [];
    }
  }

  async getExtractionStatistics(): Promise<{
    totalPages: number;
    totalListings: number;
    averageListingsPerPage: number;
    pagesWithErrors: number;
    averageConfidence: number;
  }> {
    const results = await this.getAllExtractionResults();

    if (results.length === 0) {
      return {
        totalPages: 0,
        totalListings: 0,
        averageListingsPerPage: 0,
        pagesWithErrors: 0,
        averageConfidence: 0,
      };
    }

    const totalListings = results.reduce((sum, result) => sum + result.totalEntries, 0);
    const pagesWithErrors = results.filter(result => result.errors && result.errors.length > 0).length;

    const allListings = results.flatMap(result => result.listings);
    const averageConfidence = allListings.length > 0
      ? allListings.reduce((sum, listing) => sum + listing.confidence, 0) / allListings.length
      : 0;

    return {
      totalPages: results.length,
      totalListings,
      averageListingsPerPage: totalListings / results.length,
      pagesWithErrors,
      averageConfidence,
    };
  }
}

// CLI function for direct usage
export async function runExtraction(): Promise<void> {
  const extractor = new ExtractUtility();

  try {
    await extractor.init();
    await extractor.extract();

    const stats = await extractor.getExtractionStatistics();

    console.log('\n📊 Extraction Results:');
    console.log(`📄 Pages processed: ${stats.totalPages}`);
    console.log(`📋 Total listings extracted: ${stats.totalListings}`);
    console.log(`📈 Average listings per page: ${stats.averageListingsPerPage.toFixed(1)}`);
    console.log(`❌ Pages with errors: ${stats.pagesWithErrors}`);
    console.log(`🎯 Average confidence: ${(stats.averageConfidence * 100).toFixed(1)}%`);

  } catch (error) {
    console.error('❌ Extraction failed:', error);
    Deno.exit(1);
  }
}

// CLI function for page range extraction
export async function runExtractionWithRange(
  startPage: number,
  endPage: number,
  options: { batchSize?: number; cumulative?: boolean } = {}
): Promise<void> {
  const extractor = new ExtractUtility();

  try {
    await extractor.init();

    const extractionOptions: ExtractionOptions = {
      pageRange: { startPage, endPage },
      ...(options.batchSize && { batchSize: options.batchSize }),
      ...(options.cumulative !== undefined && { cumulativeResults: options.cumulative })
    };

    await extractor.extractWithOptions(undefined, extractionOptions);

    const stats = await extractor.getExtractionStatistics();

    console.log('\n📊 Page Range Extraction Results:');
    console.log(`📄 Page range: ${startPage}-${endPage}`);
    console.log(`📋 Total listings extracted: ${stats.totalListings}`);
    console.log(`📈 Average listings per page: ${stats.averageListingsPerPage.toFixed(1)}`);
    console.log(`❌ Pages with errors: ${stats.pagesWithErrors}`);
    console.log(`🎯 Average confidence: ${(stats.averageConfidence * 100).toFixed(1)}%`);

    if (options.cumulative) {
      console.log('💾 Results saved as cumulative files (timestamped)');
    }

  } catch (error) {
    console.error('❌ Page range extraction failed:', error);
    Deno.exit(1);
  }
}
