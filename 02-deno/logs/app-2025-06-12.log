2025-06-12T00:17:34.907Z [INFO] Initializing Analyze Utility
2025-06-12T00:17:34.909Z [DEBUG] Environment variables loaded
2025-06-12T00:17:34.909Z [INFO] Configuration validation passed
2025-06-12T00:17:34.966Z [INFO] PDF has 258 pages
2025-06-12T00:17:34.966Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-12T00:17:34.966Z [INFO] Starting column analysis process
2025-06-12T00:17:34.967Z [INFO] Found existing analysis with 31 columns. Making analysis additive.
2025-06-12T00:17:34.967Z [INFO] Analyzing 5 random pages: 33, 101, 124, 137, 140
2025-06-12T00:17:34.967Z [INFO] PROGRESS: Converting sample pages to images
2025-06-12T00:17:34.967Z [DEBUG] Page 33 JPG already exists, skipping conversion (0.51MB)
2025-06-12T00:17:35.018Z [DEBUG] Using existing page 33 JPG | Data: {"pageNumber":33,"imagePath":"temp/page_33.jpg","width":1240,"height":1754,"fileSize":530312}
2025-06-12T00:17:35.018Z [DEBUG] Page 101 JPG already exists, skipping conversion (0.51MB)
2025-06-12T00:17:35.019Z [DEBUG] Using existing page 101 JPG | Data: {"pageNumber":101,"imagePath":"temp/page_101.jpg","width":1240,"height":1754,"fileSize":533396}
2025-06-12T00:17:35.019Z [DEBUG] Page 124 JPG already exists, skipping conversion (0.52MB)
2025-06-12T00:17:35.020Z [DEBUG] Using existing page 124 JPG | Data: {"pageNumber":124,"imagePath":"temp/page_124.jpg","width":1240,"height":1754,"fileSize":541743}
2025-06-12T00:17:35.020Z [DEBUG] Page 137 JPG already exists, skipping conversion (0.50MB)
2025-06-12T00:17:35.020Z [DEBUG] Using existing page 137 JPG | Data: {"pageNumber":137,"imagePath":"temp/page_137.jpg","width":1240,"height":1754,"fileSize":521478}
2025-06-12T00:17:35.021Z [DEBUG] Page 140 JPG already exists, skipping conversion (0.47MB)
2025-06-12T00:17:35.021Z [DEBUG] Using existing page 140 JPG | Data: {"pageNumber":140,"imagePath":"temp/page_140.jpg","width":1240,"height":1754,"fileSize":487936}
2025-06-12T00:17:35.021Z [INFO] PROGRESS: Analyzing images with Gemini AI
2025-06-12T00:17:35.021Z [INFO] Analyzing 5 images for column structure (attempt 1/3)
2025-06-12T00:17:35.558Z [WARN] Attempt 1 failed: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from ***********:50664 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (*************:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac
2025-06-12T00:17:35.559Z [INFO] Retrying in 2000ms...
2025-06-12T00:17:37.561Z [INFO] Analyzing 5 images for column structure (attempt 2/3)
2025-06-12T00:17:37.952Z [WARN] Attempt 2 failed: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from ***********:50667 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (172.217.0.170:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac
2025-06-12T00:17:37.952Z [INFO] Retrying in 4000ms...
2025-06-12T00:17:41.955Z [INFO] Analyzing 5 images for column structure (attempt 3/3)
2025-06-12T00:17:42.219Z [WARN] Attempt 3 failed: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from ***********:50670 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (***************:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac
2025-06-12T00:17:42.220Z [ERROR] Failed to analyze columns from images after all retries | Data: {}
2025-06-12T00:17:42.220Z [ERROR] Column analysis failed | Data: {}
2025-06-12T00:17:42.227Z [INFO] PDF service cleanup completed
2025-06-12T00:18:11.290Z [INFO] Initializing Analyze Utility
2025-06-12T00:18:11.291Z [DEBUG] Environment variables loaded
2025-06-12T00:18:11.291Z [INFO] Configuration validation passed
2025-06-12T00:18:11.345Z [INFO] PDF has 258 pages
2025-06-12T00:18:11.346Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-12T00:18:11.346Z [INFO] Starting column analysis process
2025-06-12T00:18:11.346Z [INFO] Found existing analysis with 31 columns. Making analysis additive.
2025-06-12T00:18:11.346Z [INFO] Analyzing 5 random pages: 98, 116, 168, 243, 250
2025-06-12T00:18:11.346Z [INFO] PROGRESS: Converting sample pages to images
2025-06-12T00:18:11.347Z [DEBUG] Page 98 JPG already exists, skipping conversion (0.52MB)
2025-06-12T00:18:11.365Z [DEBUG] Using existing page 98 JPG | Data: {"pageNumber":98,"imagePath":"temp/page_98.jpg","width":1240,"height":1754,"fileSize":540185}
2025-06-12T00:18:11.366Z [DEBUG] Page 116 JPG already exists, skipping conversion (0.49MB)
2025-06-12T00:18:11.366Z [DEBUG] Using existing page 116 JPG | Data: {"pageNumber":116,"imagePath":"temp/page_116.jpg","width":1240,"height":1754,"fileSize":518102}
2025-06-12T00:18:11.366Z [DEBUG] Page 168 JPG already exists, skipping conversion (0.48MB)
2025-06-12T00:18:11.367Z [DEBUG] Using existing page 168 JPG | Data: {"pageNumber":168,"imagePath":"temp/page_168.jpg","width":1240,"height":1754,"fileSize":506212}
2025-06-12T00:18:11.367Z [DEBUG] Page 243 JPG already exists, skipping conversion (0.49MB)
2025-06-12T00:18:11.367Z [DEBUG] Using existing page 243 JPG | Data: {"pageNumber":243,"imagePath":"temp/page_243.jpg","width":1240,"height":1754,"fileSize":518777}
2025-06-12T00:18:11.368Z [DEBUG] Page 250 JPG already exists, skipping conversion (0.52MB)
2025-06-12T00:18:11.368Z [DEBUG] Using existing page 250 JPG | Data: {"pageNumber":250,"imagePath":"temp/page_250.jpg","width":1240,"height":1754,"fileSize":540197}
2025-06-12T00:18:11.368Z [INFO] PROGRESS: Analyzing images with Gemini AI
2025-06-12T00:18:11.368Z [INFO] Analyzing 5 images for column structure (attempt 1/3)
2025-06-12T00:18:28.681Z [INFO] SUCCESS: Identified 31 potential columns
2025-06-12T00:18:28.682Z [DEBUG] Identified columns | Data: [{"name":"Company_Name","description":"Name of the business or company","dataType":"string","required":true,"examples":["Unitec S.p.A.","SAIJAB, Jordan","Universal Irrigation Pipe Manufacturing"]},{"name":"Address_Line_1","description":"First line of the business address","dataType":"string","required":false,"examples":["Via Provinciale Colognola","P.O. Box 249","1956 Setterington Drive"]},{"name":"Address_Line_2","description":"Second line of the business address (if applicable)","dataType":"string","required":false,"examples":["Edificio EDGERTON","Industrial Estate",""]},{"name":"City","description":"City where the business is located","dataType":"string","required":true,"examples":["Modena","Amman","Kingsville"]},{"name":"State_Province","description":"State or province where the business is located","dataType":"string","required":true,"examples":["Emilia-Romagna",null,"NY"]},{"name":"Country","description":"Country where the business is located","dataType":"string","required":true,"examples":["Italy","Jordan","USA"]},{"name":"Postal_Code","description":"Postal code of the business address","dataType":"string","required":false,"examples":["41053",null,"10925"]},{"name":"Phone","description":"Main phone number of the business","dataType":"phone","required":false,"examples":["+39 059 88 6137","+962 6 480 251","****** 326 1333"]},{"name":"Fax","description":"Fax number of the business","dataType":"phone","required":false,"examples":["+39 059 88 6137",null,"****** 326 1333"]},{"name":"Email","description":"Main email address of the business","dataType":"email","required":false,"examples":["<EMAIL>","<EMAIL>","<EMAIL>"]},{"name":"Website","description":"Website URL of the business","dataType":"url","required":false,"examples":["www.unitec-group.com","www.dip.co.jo","www.universal-irrigation.com"]},{"name":"Twitter/X","description":"Twitter/X handle or URL","dataType":"url","required":false,"examples":[null,null,null]},{"name":"Facebook","description":"Facebook page URL or handle","dataType":"url","required":false,"examples":[null,null,null]},{"name":"LinkedIn","description":"LinkedIn company or personal profile URL","dataType":"url","required":false,"examples":[null,null,null]},{"name":"Instagram","description":"Instagram handle or URL","dataType":"url","required":false,"examples":[null,null,null]},{"name":"YouTube","description":"YouTube channel URL","dataType":"url","required":false,"examples":[null,null,null]},{"name":"Other_Social_Media","description":"Other social media platforms (TikTok, Pinterest, etc.)","dataType":"string","required":false,"examples":[null,null,null]},{"name":"Founded","description":"Year the business was founded","dataType":"number","required":false,"examples":["1924","1986","1956"]},{"name":"Subsidiaries","description":"List of subsidiaries (if any)","dataType":"string","required":false,"examples":[null,null,null]},{"name":"Staff","description":"Number of employees","dataType":"number","required":false,"examples":[null,null,null]},{"name":"Category","description":"Category or industry the business belongs to","dataType":"string","required":true,"examples":["Greenhouse Technique","Greenhouse Supplies","Greenhouse Parts | Screening | Ventilation"]},{"name":"Specialties","description":"Specific services or products offered by the business","dataType":"string","required":false,"examples":["technologies for the processing and quality sorting of fruit and vegetables","parasiticides for the control of aphids",""]},{"name":"Contact_Person","description":"Name of the contact person at the business","dataType":"string","required":false,"examples":[null,null,null]},{"name":"Business_Register_Number","description":"Business registration number","dataType":"string","required":false,"examples":[null,null,null]},{"name":"VAT_Number","description":"VAT number","dataType":"string","required":false,"examples":["NL004529704B01",null,null]},{"name":"Internet","description":"Website or other online presence","dataType":"url","required":false,"examples":["www.unitec-group.com",null,null]},{"name":"Brand","description":"Brand name associated with the company","dataType":"string","required":false,"examples":["Unitec","MondiCrop","Valk Horst Systems"]},{"name":"Years_in_Business","description":"Number of years the business has been operating","dataType":"number","required":false,"examples":["75","20","60"]},{"name":"Contacts","description":"Additional contact information","dataType":"string","required":false,"examples":[null,null,null]},{"name":"Products","description":"List of products offered by the business","dataType":"string","required":false,"examples":[null,null,null]},{"name":"Additional_Contact_Information","description":"Additional contact details beyond phone, fax, and email","dataType":"string","required":false,"examples":[null,null,null]}]
2025-06-12T00:18:28.683Z [INFO] Added required columns: Other_Social_Media
2025-06-12T00:18:28.685Z [DEBUG] JSON file written: output/analysis/column_analysis_2025-06-12T00-18-28-684Z.json
2025-06-12T00:18:28.685Z [INFO] Analysis result saved to: output/analysis/column_analysis_2025-06-12T00-18-28-684Z.json
2025-06-12T00:18:28.686Z [DEBUG] JSON file written: output/analysis/latest.json
2025-06-12T00:18:28.686Z [DEBUG] Latest analysis result saved
2025-06-12T00:18:28.686Z [INFO] SUCCESS: Column analysis completed. Identified 32 columns with 100.0% confidence
2025-06-12T00:18:28.692Z [INFO] PDF service cleanup completed
2025-06-12T00:23:30.695Z [INFO] Initializing Analyze Utility
2025-06-12T00:23:30.696Z [DEBUG] Environment variables loaded
2025-06-12T00:23:30.696Z [INFO] Configuration validation passed
2025-06-12T00:23:30.752Z [INFO] PDF has 258 pages
2025-06-12T00:23:30.752Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-12T00:23:30.752Z [INFO] Starting column analysis process
2025-06-12T00:23:30.752Z [INFO] Found existing analysis with 29 columns. Making analysis additive.
2025-06-12T00:23:30.753Z [INFO] Analyzing 5 random pages: 7, 66, 83, 115, 152
2025-06-12T00:23:30.753Z [INFO] PROGRESS: Converting sample pages to images
2025-06-12T00:23:30.753Z [DEBUG] Page 7 JPG already exists, skipping conversion (0.48MB)
2025-06-12T00:23:30.772Z [DEBUG] Using existing page 7 JPG | Data: {"pageNumber":7,"imagePath":"temp/page_7.jpg","width":1240,"height":1754,"fileSize":500509}
2025-06-12T00:23:30.772Z [DEBUG] Page 66 JPG already exists, skipping conversion (0.51MB)
2025-06-12T00:23:30.772Z [DEBUG] Using existing page 66 JPG | Data: {"pageNumber":66,"imagePath":"temp/page_66.jpg","width":1240,"height":1754,"fileSize":532734}
2025-06-12T00:23:30.772Z [DEBUG] Page 83 JPG already exists, skipping conversion (0.50MB)
2025-06-12T00:23:30.773Z [DEBUG] Using existing page 83 JPG | Data: {"pageNumber":83,"imagePath":"temp/page_83.jpg","width":1240,"height":1754,"fileSize":528978}
2025-06-12T00:23:30.773Z [DEBUG] Page 115 JPG already exists, skipping conversion (0.48MB)
2025-06-12T00:23:30.774Z [DEBUG] Using existing page 115 JPG | Data: {"pageNumber":115,"imagePath":"temp/page_115.jpg","width":1240,"height":1754,"fileSize":500800}
2025-06-12T00:23:30.774Z [DEBUG] Page 152 JPG already exists, skipping conversion (0.46MB)
2025-06-12T00:23:30.774Z [DEBUG] Using existing page 152 JPG | Data: {"pageNumber":152,"imagePath":"temp/page_152.jpg","width":1240,"height":1754,"fileSize":481899}
2025-06-12T00:23:30.774Z [INFO] PROGRESS: Analyzing images with Gemini AI
2025-06-12T00:23:30.774Z [INFO] Analyzing 5 images for column structure (attempt 1/3)
2025-06-12T00:23:48.367Z [INFO] SUCCESS: Identified 31 potential columns
2025-06-12T00:23:48.368Z [DEBUG] Identified columns | Data: [{"name":"Company_Name","description":"Name of the business or company","dataType":"string","required":true,"examples":["Fenix Seeds SRL","Floritec","Gartenbau Silber Gbr"]},{"name":"Address_Line_1","description":"First line of the business address","dataType":"string","required":false,"examples":["251 Floris","Gartengestaltung","45-47 Rue d'Avignon"]},{"name":"Address_Line_2","description":"Second line of the business address (if applicable)","dataType":"string","required":false,"examples":["",""]},{"name":"City","description":"City where the business is located","dataType":"string","required":true,"examples":["Heemstede","Heide","Avignon"]},{"name":"State_Province","description":"State or province where the business is located","dataType":"string","required":true,"examples":["North Holland","","Rhone"]},{"name":"Country","description":"Country where the business is located","dataType":"string","required":true,"examples":["Netherlands","Germany","France"]},{"name":"Postal_Code","description":"Postal code of the business address","dataType":"string","required":false,"examples":["2151","25837","84130"]},{"name":"Phone","description":"Main phone number of the business","dataType":"phone","required":false,"examples":["+**************","+**************","+33 4 90 240 240"]},{"name":"Fax","description":"Fax number of the business","dataType":"phone","required":false,"examples":["+31 20 307 083","+49 152 292 83","+33 4 90 240 240"]},{"name":"Email","description":"Main email address of the business","dataType":"email","required":false,"examples":["<EMAIL>","<EMAIL>","<EMAIL>"]},{"name":"Website","description":"Website URL of the business","dataType":"url","required":false,"examples":["www.fenixseeds.com","www.floritec.eu","www.gartenbau-silbergbr.de"]},{"name":"Twitter/X","description":"Twitter/X handle or URL","dataType":"url","required":false,"examples":["@fenixseeds",null,null]},{"name":"Facebook","description":"Facebook page URL or handle","dataType":"url","required":false,"examples":[null,null,null]},{"name":"LinkedIn","description":"LinkedIn company or personal profile URL","dataType":"url","required":false,"examples":[null,null,null]},{"name":"Instagram","description":"Instagram handle or URL","dataType":"url","required":false,"examples":[null,null,null]},{"name":"YouTube","description":"YouTube channel URL","dataType":"url","required":false,"examples":[null,null,null]},{"name":"Other_Social_Media","description":"Other social media accounts not covered by specific columns (TikTok, Pinterest, etc.)","dataType":"string","required":false,"examples":[null,null,null]},{"name":"Founded","description":"Year the business was founded","dataType":"number","required":false,"examples":["1969","2007","1993"]},{"name":"Years_in_Business","description":"Number of years the business has been operating","dataType":"number","required":false,"examples":[null,null,null]},{"name":"Staff","description":"Number of employees","dataType":"number","required":false,"examples":[null,null,null]},{"name":"Contact_Person","description":"Name of the contact person at the business","dataType":"string","required":false,"examples":["Ivana Marchese","Daphne Hoogeveen","Janina Steffens"]},{"name":"Additional_Contact_Information","description":"Additional contact details like phone, fax, and email","dataType":"string","required":false,"examples":[null,null,null]},{"name":"Business_Register_Number","description":"Business registration number","dataType":"string","required":false,"examples":[null,null,null]},{"name":"VAT_Number","description":"VAT number","dataType":"string","required":false,"examples":["NL8520.219.75",null,null]},{"name":"Brand","description":"Brand name associated with the company","dataType":"string","required":false,"examples":[null,null,null]},{"name":"Products","description":"List of products offered by the business","dataType":"string","required":false,"examples":["seeds","chrysanthemum cutflower and pot plants","ornamental plants"]},{"name":"Subsidiaries","description":"List of subsidiaries (if any)","dataType":"string","required":false,"examples":["Floricultura Pacific, Floricultura Orchidee",null,null]},{"name":"Specialties","description":"Specific services or products offered by the business","dataType":"string","required":false,"examples":["Breeder","Top quality in ericaceous young plants",null]},{"name":"Category","description":"Category or industry the business belongs to","dataType":"string","required":true,"examples":["Breeders","Greenhouse Technique","Greenhouse Supplies"]},{"name":"Category_Header","description":"Header indicating the category or industry group","dataType":"string","required":false,"examples":["Breeders","Greenhouse Technique","Greenhouse Supplies"]},{"name":"Industry_Group","description":"Higher-level industry grouping (e.g., Agriculture, Horticulture)","dataType":"string","required":false,"examples":["Horticulture","Horticulture","Horticulture"]}]
2025-06-12T00:23:48.368Z [INFO] Added required columns: Other_Social_Media
2025-06-12T00:23:48.370Z [DEBUG] JSON file written: output/analysis/column_analysis_2025-06-12T00-23-48-369Z.json
2025-06-12T00:23:48.370Z [INFO] Analysis result saved to: output/analysis/column_analysis_2025-06-12T00-23-48-369Z.json
2025-06-12T00:23:48.370Z [DEBUG] JSON file written: output/analysis/latest.json
2025-06-12T00:23:48.370Z [DEBUG] Latest analysis result saved
2025-06-12T00:23:48.371Z [INFO] SUCCESS: Column analysis completed. Identified 32 columns with 100.0% confidence
2025-06-12T00:23:48.379Z [INFO] PDF service cleanup completed
2025-06-12T00:24:44.337Z [INFO] Initializing Extract Utility
2025-06-12T00:24:44.338Z [DEBUG] Environment variables loaded
2025-06-12T00:24:44.338Z [INFO] Configuration validation passed
2025-06-12T00:24:44.338Z [INFO] Initializing Analyze Utility
2025-06-12T00:24:44.338Z [INFO] Configuration validation passed
2025-06-12T00:24:44.393Z [INFO] PDF has 258 pages
2025-06-12T00:24:44.393Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-12T00:24:44.446Z [INFO] PDF has 258 pages
2025-06-12T00:24:44.447Z [INFO] SUCCESS: Extract Utility initialized successfully
2025-06-12T00:24:44.447Z [INFO] Starting data extraction process
2025-06-12T00:24:44.447Z [INFO] Using analysis with 29 columns
2025-06-12T00:24:44.448Z [INFO] Processing page range: 1-2 (2 pages)
2025-06-12T00:24:44.448Z [INFO] PROGRESS: Processing batch 1 (2/2)
2025-06-12T00:24:44.449Z [DEBUG] Page 1 JPG already exists, skipping conversion (0.50MB)
2025-06-12T00:24:44.470Z [DEBUG] Using existing page 1 JPG | Data: {"pageNumber":1,"imagePath":"temp/page_1.jpg","width":1240,"height":1754,"fileSize":520421}
2025-06-12T00:24:44.471Z [INFO] PROGRESS: Extracting data from page 1
2025-06-12T00:24:45.004Z [ERROR] Failed to extract data from page 1 | Data: {}
2025-06-12T00:24:45.004Z [ERROR] ❌ Attempt 1/3 failed for page 1: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from ***********:51227 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (***************:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac | Data: {}
2025-06-12T00:24:45.004Z [INFO] ⏳ Waiting 1000ms before retry...
2025-06-12T00:24:46.007Z [INFO] 🔄 Retry attempt 2/3 for page 1
2025-06-12T00:24:46.008Z [DEBUG] Page 1 JPG already exists, skipping conversion (0.50MB)
2025-06-12T00:24:46.009Z [DEBUG] Using existing page 1 JPG | Data: {"pageNumber":1,"imagePath":"temp/page_1.jpg","width":1240,"height":1754,"fileSize":520421}
2025-06-12T00:24:46.009Z [INFO] PROGRESS: Extracting data from page 1
2025-06-12T00:25:17.092Z [INFO] SUCCESS: Extracted 11 listings from page 1
2025-06-12T00:25:17.093Z [DEBUG] Page 1 processed: 11 entries in 31086ms
2025-06-12T00:25:17.093Z [INFO] SUCCESS: ✅ Page 1 succeeded on attempt 2
2025-06-12T00:25:17.094Z [DEBUG] Page 1 JPG already exists, skipping conversion (0.50MB)
2025-06-12T00:25:17.095Z [DEBUG] Using existing page 1 JPG | Data: {"pageNumber":1,"imagePath":"temp/page_1.jpg","width":1240,"height":1754,"fileSize":520421}
2025-06-12T00:25:17.096Z [DEBUG] Detecting category from page 1 (attempt 1/3)
2025-06-12T00:25:18.932Z [DEBUG] Detected category "Breeders" on page 1
2025-06-12T00:25:18.933Z [DEBUG] JSON file written: output/category.json
2025-06-12T00:25:18.933Z [DEBUG] Detected category "Breeders" for page 1
2025-06-12T00:25:18.934Z [DEBUG] Page 2 JPG already exists, skipping conversion (0.40MB)
2025-06-12T00:25:18.936Z [DEBUG] Using existing page 2 JPG | Data: {"pageNumber":2,"imagePath":"temp/page_2.jpg","width":1240,"height":1754,"fileSize":422291}
2025-06-12T00:25:18.936Z [INFO] PROGRESS: Extracting data from page 2
2025-06-12T00:25:34.054Z [INFO] SUCCESS: Extracted 5 listings from page 2
2025-06-12T00:25:34.055Z [DEBUG] Page 2 processed: 5 entries in 15121ms
2025-06-12T00:25:34.055Z [DEBUG] Page 2 JPG already exists, skipping conversion (0.40MB)
2025-06-12T00:25:34.057Z [DEBUG] Using existing page 2 JPG | Data: {"pageNumber":2,"imagePath":"temp/page_2.jpg","width":1240,"height":1754,"fileSize":422291}
2025-06-12T00:25:34.057Z [DEBUG] Detecting category from page 2 (attempt 1/3)
2025-06-12T00:25:35.801Z [DEBUG] Detected category "Breeders" on page 2
2025-06-12T00:25:35.802Z [DEBUG] JSON file written: output/category.json
2025-06-12T00:25:35.802Z [DEBUG] Detected category "Breeders" for page 2
2025-06-12T00:25:35.803Z [DEBUG] JSON file written: output/extracted/page_001.json
2025-06-12T00:25:35.804Z [DEBUG] JSON file written: output/extracted/page_002.json
2025-06-12T00:25:35.804Z [DEBUG] Saved 2 standard extraction results
2025-06-12T00:25:35.804Z [INFO] SUCCESS: Data extraction completed. Processed 2 pages
2025-06-12T00:25:35.825Z [INFO] PDF service cleanup completed
2025-06-12T00:26:45.042Z [INFO] Initializing Extract Utility
2025-06-12T00:26:45.043Z [DEBUG] Environment variables loaded
2025-06-12T00:26:45.043Z [INFO] Configuration validation passed
2025-06-12T00:26:45.043Z [INFO] Initializing Analyze Utility
2025-06-12T00:26:45.043Z [INFO] Configuration validation passed
2025-06-12T00:26:45.098Z [INFO] PDF has 258 pages
2025-06-12T00:26:45.098Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-12T00:26:45.152Z [INFO] PDF has 258 pages
2025-06-12T00:26:45.152Z [INFO] SUCCESS: Extract Utility initialized successfully
2025-06-12T00:26:45.152Z [INFO] Starting data extraction process
2025-06-12T00:26:45.153Z [INFO] Using analysis with 29 columns
2025-06-12T00:26:45.153Z [INFO] Processing page range: 2-2 (1 pages)
2025-06-12T00:26:45.153Z [INFO] PROGRESS: Processing batch 1 (1/1)
2025-06-12T00:26:45.154Z [DEBUG] Page 2 JPG already exists, skipping conversion (0.40MB)
2025-06-12T00:26:45.173Z [DEBUG] Using existing page 2 JPG | Data: {"pageNumber":2,"imagePath":"temp/page_2.jpg","width":1240,"height":1754,"fileSize":422291}
2025-06-12T00:26:45.173Z [INFO] PROGRESS: Extracting data from page 2
2025-06-12T00:27:00.786Z [INFO] SUCCESS: Extracted 5 listings from page 2
2025-06-12T00:27:00.787Z [DEBUG] Page 2 processed: 5 entries in 15634ms
2025-06-12T00:27:00.787Z [DEBUG] Page 2 JPG already exists, skipping conversion (0.40MB)
2025-06-12T00:27:00.788Z [DEBUG] Using existing page 2 JPG | Data: {"pageNumber":2,"imagePath":"temp/page_2.jpg","width":1240,"height":1754,"fileSize":422291}
2025-06-12T00:27:00.788Z [DEBUG] Detecting category from page 2 (attempt 1/3)
2025-06-12T00:27:02.217Z [DEBUG] Detected category "Breeders" on page 2
2025-06-12T00:27:02.218Z [DEBUG] JSON file written: output/category.json
2025-06-12T00:27:02.218Z [DEBUG] Detected category "Breeders" for page 2
2025-06-12T00:27:02.219Z [DEBUG] JSON file written: output/extracted/page_002.json
2025-06-12T00:27:02.219Z [DEBUG] Saved 1 standard extraction results
2025-06-12T00:27:02.219Z [INFO] SUCCESS: Data extraction completed. Processed 1 pages
2025-06-12T00:27:02.224Z [INFO] PDF service cleanup completed
2025-06-12T00:27:12.294Z [INFO] Initializing Extract Utility
2025-06-12T00:27:12.296Z [DEBUG] Environment variables loaded
2025-06-12T00:27:12.296Z [INFO] Configuration validation passed
2025-06-12T00:27:12.296Z [INFO] Initializing Analyze Utility
2025-06-12T00:27:12.296Z [INFO] Configuration validation passed
2025-06-12T00:27:12.352Z [INFO] PDF has 258 pages
2025-06-12T00:27:12.353Z [INFO] SUCCESS: Analyze Utility initialized successfully
2025-06-12T00:27:12.404Z [INFO] PDF has 258 pages
2025-06-12T00:27:12.404Z [INFO] SUCCESS: Extract Utility initialized successfully
2025-06-12T00:27:12.404Z [INFO] Starting data extraction process
2025-06-12T00:27:12.405Z [INFO] Using analysis with 29 columns
2025-06-12T00:27:12.405Z [INFO] Processing page range: 1-2 (2 pages)
2025-06-12T00:27:12.405Z [INFO] PROGRESS: Processing batch 1 (2/2)
2025-06-12T00:27:12.406Z [DEBUG] Page 1 JPG already exists, skipping conversion (0.50MB)
2025-06-12T00:27:12.423Z [DEBUG] Using existing page 1 JPG | Data: {"pageNumber":1,"imagePath":"temp/page_1.jpg","width":1240,"height":1754,"fileSize":520421}
2025-06-12T00:27:12.423Z [INFO] PROGRESS: Extracting data from page 1
2025-06-12T00:27:44.467Z [INFO] SUCCESS: Extracted 11 listings from page 1
2025-06-12T00:27:44.469Z [DEBUG] Page 1 processed: 11 entries in 32064ms
2025-06-12T00:27:44.469Z [DEBUG] Page 1 JPG already exists, skipping conversion (0.50MB)
2025-06-12T00:27:44.471Z [DEBUG] Using existing page 1 JPG | Data: {"pageNumber":1,"imagePath":"temp/page_1.jpg","width":1240,"height":1754,"fileSize":520421}
2025-06-12T00:27:44.471Z [DEBUG] Detecting category from page 1 (attempt 1/3)
2025-06-12T00:27:44.605Z [WARN] Category detection attempt 1 failed for page 1: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: error sending request from ***********:51376 for https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent (*************:443): client error (SendRequest): connection error: received fatal alert: BadRecordMac
2025-06-12T00:27:44.606Z [DEBUG] Retrying in 1000ms...
2025-06-12T00:27:45.609Z [DEBUG] Detecting category from page 1 (attempt 2/3)
2025-06-12T00:27:47.337Z [DEBUG] Detected category "Breeders" on page 1
2025-06-12T00:27:47.339Z [DEBUG] JSON file written: output/category.json
2025-06-12T00:27:47.339Z [DEBUG] Detected category "Breeders" for page 1
2025-06-12T00:27:47.340Z [DEBUG] Page 2 JPG already exists, skipping conversion (0.40MB)
2025-06-12T00:27:47.341Z [DEBUG] Using existing page 2 JPG | Data: {"pageNumber":2,"imagePath":"temp/page_2.jpg","width":1240,"height":1754,"fileSize":422291}
2025-06-12T00:27:47.341Z [INFO] PROGRESS: Extracting data from page 2
2025-06-12T00:28:02.989Z [INFO] SUCCESS: Extracted 5 listings from page 2
2025-06-12T00:28:02.990Z [DEBUG] Page 2 processed: 5 entries in 15651ms
2025-06-12T00:28:02.991Z [DEBUG] Page 2 JPG already exists, skipping conversion (0.40MB)
2025-06-12T00:28:02.992Z [DEBUG] Using existing page 2 JPG | Data: {"pageNumber":2,"imagePath":"temp/page_2.jpg","width":1240,"height":1754,"fileSize":422291}
2025-06-12T00:28:02.993Z [DEBUG] Detecting category from page 2 (attempt 1/3)
2025-06-12T00:28:04.793Z [DEBUG] Detected category "Breeders" on page 2
2025-06-12T00:28:04.794Z [DEBUG] JSON file written: output/category.json
2025-06-12T00:28:04.794Z [DEBUG] Detected category "Breeders" for page 2
2025-06-12T00:28:04.795Z [DEBUG] JSON file written: output/extracted/page_001.json
2025-06-12T00:28:04.796Z [DEBUG] JSON file written: output/extracted/page_002.json
2025-06-12T00:28:04.796Z [DEBUG] Saved 2 standard extraction results
2025-06-12T00:28:04.796Z [INFO] SUCCESS: Data extraction completed. Processed 2 pages
2025-06-12T00:28:04.807Z [INFO] PDF service cleanup completed
