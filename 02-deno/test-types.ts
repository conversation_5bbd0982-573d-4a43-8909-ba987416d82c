#!/usr/bin/env -S deno run --allow-all

// Simple test to verify all types compile correctly
console.log('🧪 Testing TypeScript compilation...\n');

try {
  // Test importing all main modules
  console.log('📦 Testing imports...');
  
  const { Config } = await import('./src/config.ts');
  console.log('✅ Config import works');
  
  const { AnalyzeUtility } = await import('./src/analyze.ts');
  console.log('✅ AnalyzeUtility import works');
  
  const { ExtractUtility } = await import('./src/extract.ts');
  console.log('✅ ExtractUtility import works');
  
  const { CombineUtility } = await import('./src/combine.ts');
  console.log('✅ CombineUtility import works');
  
  // Test services
  const { PDFService } = await import('./src/services/pdf.ts');
  console.log('✅ PDFService import works');
  
  const { GeminiService } = await import('./src/services/gemini.ts');
  console.log('✅ GeminiService import works');
  
  const { ExcelService } = await import('./src/services/excel.ts');
  console.log('✅ ExcelService import works');
  
  // Test utils
  const { logger } = await import('./src/utils/logger.ts');
  console.log('✅ Logger import works');
  
  const { FileUtils } = await import('./src/utils/file.ts');
  console.log('✅ FileUtils import works');
  
  // Test types
  const types = await import('./src/types.ts');
  console.log('✅ Types import works');
  
  console.log('\n🎉 All TypeScript imports successful!');
  console.log('✅ No type errors detected');
  
} catch (error) {
  console.error('❌ TypeScript compilation failed:', error);
  Deno.exit(1);
}
