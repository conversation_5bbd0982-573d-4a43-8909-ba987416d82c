              @google/generative-ai       @google+generative-ai@0.24.1	       commander       commander@14.0.0       exceljs
       exceljs@4.4.0       sharp       sharp@0.34.2X              @fast-csv/format       @fast-csv+format@4.3.5       @fast-csv/parse       @fast-csv+parse@4.3.6       @img/sharp-darwin-arm64       @img+sharp-darwin-arm64@0.34.2       @img/sharp-libvips-darwin-arm64%       @img+sharp-libvips-darwin-arm64@1.1.0       @types/node       @types+node@14.18.63       archiver       archiver@5.3.2       archiver-utils       archiver-utils@3.0.4       async       async@3.2.6       balanced-match       balanced-match@1.0.2	       base64-js       base64-js@1.5.1       big-integer       big-integer@1.6.52       binary       binary@0.3.0       bl       bl@4.1.0       bluebird       b<PERSON>bird@3.4.7       brace-expansion       brace-expansion@2.0.2       buffer       buffer@5.7.1       buffer-crc32       buffer-crc32@0.2.13       buffer-indexof-polyfill       buffer-indexof-polyfill@1.0.2       buffers
       buffers@0.1.1       chainsaw       chainsaw@0.1.0       color       color@4.2.3
       color-convert       color-convert@2.0.1
       color-name       color-name@1.1.4       color-string       color-string@1.9.1       compress-commons       compress-commons@4.1.2
       concat-map       concat-map@0.0.1       core-util-is       core-util-is@1.0.3       crc-32       crc-32@1.2.2       crc32-stream       crc32-stream@4.0.3       dayjs
       dayjs@1.11.13       detect-libc       detect-libc@2.0.4	       duplexer2       duplexer2@0.1.4
       end-of-stream       end-of-stream@1.4.4       fast-csv       fast-csv@4.3.6       fs-constants       fs-constants@1.0.0       fs.realpath       fs.realpath@1.0.0       fstream       fstream@1.0.12       glob
       glob@7.2.3       graceful-fs       graceful-fs@4.2.11       ieee754
       ieee754@1.2.1	       immediate       immediate@3.0.6       inflight       inflight@1.0.6       inherits       inherits@2.0.4       is-arrayish       is-arrayish@0.3.2       isarray
       isarray@1.0.0       jszip       jszip@3.10.1
       lazystream       lazystream@1.0.1       lie	       lie@3.3.0
       listenercount       listenercount@1.0.1       lodash.defaults       lodash.defaults@4.2.0       lodash.difference       lodash.difference@4.5.0       lodash.escaperegexp       lodash.escaperegexp@4.1.2       lodash.flatten       lodash.flatten@4.4.0       lodash.groupby       lodash.groupby@4.6.0       lodash.isboolean       lodash.isboolean@3.0.3       lodash.isequal       lodash.isequal@4.5.0       lodash.isfunction       lodash.isfunction@3.0.9       lodash.isnil       lodash.isnil@4.0.0       lodash.isplainobject       lodash.isplainobject@4.0.6       lodash.isundefined       lodash.isundefined@3.0.1       lodash.union       lodash.union@4.6.0       lodash.uniq       lodash.uniq@4.5.0	       minimatch       minimatch@5.1.6       minimist       minimist@1.2.8       mkdirp       mkdirp@0.5.6       normalize-path       normalize-path@3.0.0       once
       once@1.4.0       pako       pako@1.0.11       path-is-absolute       path-is-absolute@1.0.1       process-nextick-args       process-nextick-args@2.0.1       readable-stream       readable-stream@3.6.2       readdir-glob       readdir-glob@1.1.3       rimraf       rimraf@2.7.1       safe-buffer       safe-buffer@5.2.1       saxes       saxes@5.0.1       semver       semver@7.7.2       setimmediate       setimmediate@1.0.5       simple-swizzle       simple-swizzle@0.2.2       string_decoder       string_decoder@1.3.0
       tar-stream       tar-stream@2.2.0       tmp	       tmp@0.2.3       traverse       traverse@0.3.9       unzipper       unzipper@0.10.14       util-deprecate       util-deprecate@1.0.2       uuid
       uuid@8.3.2       wrappy       wrappy@1.0.2       xmlchars       xmlchars@2.2.0
       zip-stream       zip-stream@4.1.1b              @fast-csv+format@4.3.5              @types/node       @types+node@14.18.63       lodash.escaperegexp       lodash.escaperegexp@4.1.2       lodash.isboolean       lodash.isboolean@3.0.3       lodash.isequal       lodash.isequal@4.5.0       lodash.isfunction       lodash.isfunction@3.0.9       lodash.isnil       lodash.isnil@4.0.0       @fast-csv+parse@4.3.6              @types/node       @types+node@14.18.63       lodash.escaperegexp       lodash.escaperegexp@4.1.2       lodash.groupby       lodash.groupby@4.6.0       lodash.isfunction       lodash.isfunction@3.0.9       lodash.isnil       lodash.isnil@4.0.0       lodash.isundefined       lodash.isundefined@3.0.1       lodash.uniq       lodash.uniq@4.5.0       @google+generative-ai@0.24.1               @img+sharp-darwin-arm64@0.34.2              @img/sharp-libvips-darwin-arm64%       @img+sharp-libvips-darwin-arm64@1.1.0%       @img+sharp-libvips-darwin-arm64@1.1.0               @types+node@14.18.63               archiver-utils@2.1.0
              glob
       glob@7.2.3       graceful-fs       graceful-fs@4.2.11
       lazystream       lazystream@1.0.1       lodash.defaults       lodash.defaults@4.2.0       lodash.difference       lodash.difference@4.5.0       lodash.flatten       lodash.flatten@4.4.0       lodash.isplainobject       lodash.isplainobject@4.0.6       lodash.union       lodash.union@4.6.0       normalize-path       normalize-path@3.0.0       readable-stream       readable-stream@2.3.8       archiver-utils@3.0.4
              glob
       glob@7.2.3       graceful-fs       graceful-fs@4.2.11
       lazystream       lazystream@1.0.1       lodash.defaults       lodash.defaults@4.2.0       lodash.difference       lodash.difference@4.5.0       lodash.flatten       lodash.flatten@4.4.0       lodash.isplainobject       lodash.isplainobject@4.0.6       lodash.union       lodash.union@4.6.0       normalize-path       normalize-path@3.0.0       readable-stream       readable-stream@3.6.2       archiver@5.3.2              archiver-utils       archiver-utils@2.1.0       async       async@3.2.6       buffer-crc32       buffer-crc32@0.2.13       readable-stream       readable-stream@3.6.2       readdir-glob       readdir-glob@1.1.3
       tar-stream       tar-stream@2.2.0
       zip-stream       zip-stream@4.1.1       async@3.2.6               balanced-match@1.0.2               base64-js@1.5.1               big-integer@1.6.52               binary@0.3.0              buffers
       buffers@0.1.1       chainsaw       chainsaw@0.1.0       bl@4.1.0              buffer       buffer@5.7.1       inherits       inherits@2.0.4       readable-stream       readable-stream@3.6.2       bluebird@3.4.7               brace-expansion@1.1.12              balanced-match       balanced-match@1.0.2
       concat-map       concat-map@0.0.1       brace-expansion@2.0.2              balanced-match       balanced-match@1.0.2       buffer-crc32@0.2.13               buffer-indexof-polyfill@1.0.2               buffer@5.7.1       	       base64-js       base64-js@1.5.1       ieee754
       ieee754@1.2.1
       buffers@0.1.1               chainsaw@0.1.0              traverse       traverse@0.3.9       color-convert@2.0.1       
       color-name       color-name@1.1.4       color-name@1.1.4               color-string@1.9.1       
       color-name       color-name@1.1.4       simple-swizzle       simple-swizzle@0.2.2       color@4.2.3       
       color-convert       color-convert@2.0.1       color-string       color-string@1.9.1       commander@14.0.0               compress-commons@4.1.2              buffer-crc32       buffer-crc32@0.2.13       crc32-stream       crc32-stream@4.0.3       normalize-path       normalize-path@3.0.0       readable-stream       readable-stream@3.6.2       concat-map@0.0.1               core-util-is@1.0.3               crc-32@1.2.2               crc32-stream@4.0.3              crc-32       crc-32@1.2.2       readable-stream       readable-stream@3.6.2
       dayjs@1.11.13               detect-libc@2.0.4               duplexer2@0.1.4              readable-stream       readable-stream@2.3.8       end-of-stream@1.4.4              once
       once@1.4.0
       exceljs@4.4.0	              archiver       archiver@5.3.2       dayjs
       dayjs@1.11.13       fast-csv       fast-csv@4.3.6       jszip       jszip@3.10.1       readable-stream       readable-stream@3.6.2       saxes       saxes@5.0.1       tmp	       tmp@0.2.3       unzipper       unzipper@0.10.14       uuid
       uuid@8.3.2       fast-csv@4.3.6              @fast-csv/format       @fast-csv+format@4.3.5       @fast-csv/parse       @fast-csv+parse@4.3.6       fs-constants@1.0.0               fs.realpath@1.0.0               fstream@1.0.12              graceful-fs       graceful-fs@4.2.11       inherits       inherits@2.0.4       mkdirp       mkdirp@0.5.6       rimraf       rimraf@2.7.1
       glob@7.2.3              fs.realpath       fs.realpath@1.0.0       inflight       inflight@1.0.6       inherits       inherits@2.0.4	       minimatch       minimatch@3.1.2       once
       once@1.4.0       path-is-absolute       path-is-absolute@1.0.1       graceful-fs@4.2.11        
       ieee754@1.2.1               immediate@3.0.6               inflight@1.0.6              once
       once@1.4.0       wrappy       wrappy@1.0.2       inherits@2.0.4               is-arrayish@0.3.2        
       isarray@1.0.0               jszip@3.10.1              lie	       lie@3.3.0       pako       pako@1.0.11       readable-stream       readable-stream@2.3.8       setimmediate       setimmediate@1.0.5       lazystream@1.0.1              readable-stream       readable-stream@2.3.8	       lie@3.3.0       	       immediate       immediate@3.0.6       listenercount@1.0.1               lodash.defaults@4.2.0               lodash.difference@4.5.0               lodash.escaperegexp@4.1.2               lodash.flatten@4.4.0               lodash.groupby@4.6.0               lodash.isboolean@3.0.3               lodash.isequal@4.5.0               lodash.isfunction@3.0.9               lodash.isnil@4.0.0               lodash.isplainobject@4.0.6               lodash.isundefined@3.0.1               lodash.union@4.6.0               lodash.uniq@4.5.0               minimatch@3.1.2              brace-expansion       brace-expansion@1.1.12       minimatch@5.1.6              brace-expansion       brace-expansion@2.0.2       minimist@1.2.8               mkdirp@0.5.6              minimist       minimist@1.2.8       normalize-path@3.0.0        
       once@1.4.0              wrappy       wrappy@1.0.2       pako@1.0.11               path-is-absolute@1.0.1               process-nextick-args@2.0.1               readable-stream@2.3.8              core-util-is       core-util-is@1.0.3       inherits       inherits@2.0.4       isarray
       isarray@1.0.0       process-nextick-args       process-nextick-args@2.0.1       safe-buffer       safe-buffer@5.1.2       string_decoder       string_decoder@1.1.1       util-deprecate       util-deprecate@1.0.2       readable-stream@3.6.2              inherits       inherits@2.0.4       string_decoder       string_decoder@1.3.0       util-deprecate       util-deprecate@1.0.2       readdir-glob@1.1.3       	       minimatch       minimatch@5.1.6       rimraf@2.7.1              glob
       glob@7.2.3       safe-buffer@5.1.2               safe-buffer@5.2.1               saxes@5.0.1              xmlchars       xmlchars@2.2.0       semver@7.7.2               setimmediate@1.0.5               sharp@0.34.2              @img/sharp-darwin-arm64       @img+sharp-darwin-arm64@0.34.2       @img/sharp-libvips-darwin-arm64%       @img+sharp-libvips-darwin-arm64@1.1.0       color       color@4.2.3       detect-libc       detect-libc@2.0.4       semver       semver@7.7.2       simple-swizzle@0.2.2              is-arrayish       is-arrayish@0.3.2       string_decoder@1.1.1              safe-buffer       safe-buffer@5.1.2       string_decoder@1.3.0              safe-buffer       safe-buffer@5.2.1       tar-stream@2.2.0              bl       bl@4.1.0
       end-of-stream       end-of-stream@1.4.4       fs-constants       fs-constants@1.0.0       inherits       inherits@2.0.4       readable-stream       readable-stream@3.6.2	       tmp@0.2.3               traverse@0.3.9               unzipper@0.10.14
              big-integer       big-integer@1.6.52       binary       binary@0.3.0       bluebird       bluebird@3.4.7       buffer-indexof-polyfill       buffer-indexof-polyfill@1.0.2	       duplexer2       duplexer2@0.1.4       fstream       fstream@1.0.12       graceful-fs       graceful-fs@4.2.11
       listenercount       listenercount@1.0.1       readable-stream       readable-stream@2.3.8       setimmediate       setimmediate@1.0.5       util-deprecate@1.0.2        
       uuid@8.3.2               wrappy@1.0.2               xmlchars@2.2.0               zip-stream@4.1.1              archiver-utils       archiver-utils@3.0.4       compress-commons       compress-commons@4.1.2       readable-stream       readable-stream@3.6.2