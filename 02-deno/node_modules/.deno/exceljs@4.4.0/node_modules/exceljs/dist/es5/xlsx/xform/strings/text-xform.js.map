{"version": 3, "file": "text-xform.js", "names": ["BaseXform", "require", "TextXform", "tag", "render", "xmlStream", "model", "openNode", "test", "addAttribute", "writeText", "closeNode", "_text", "join", "replace", "$0", "$1", "String", "fromCharCode", "parseInt", "parseOpen", "node", "name", "parseText", "text", "push", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/strings/text-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\n//   <t xml:space=\"preserve\"> is </t>\n\nclass TextXform extends BaseXform {\n  get tag() {\n    return 't';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode('t');\n    if (/^\\s|\\n|\\s$/.test(model)) {\n      xmlStream.addAttribute('xml:space', 'preserve');\n    }\n    xmlStream.writeText(model);\n    xmlStream.closeNode();\n  }\n\n  get model() {\n    return this._text\n      .join('')\n      .replace(/_x([0-9A-F]{4})_/g, ($0, $1) => String.fromCharCode(parseInt($1, 16)));\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case 't':\n        this._text = [];\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    this._text.push(text);\n  }\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = TextXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAE1C;;AAEA,MAAMC,SAAS,SAASF,SAAS,CAAC;EAChC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,GAAG;EACZ;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,GAAG,CAAC;IACvB,IAAI,YAAY,CAACC,IAAI,CAACF,KAAK,CAAC,EAAE;MAC5BD,SAAS,CAACI,YAAY,CAAC,WAAW,EAAE,UAAU,CAAC;IACjD;IACAJ,SAAS,CAACK,SAAS,CAACJ,KAAK,CAAC;IAC1BD,SAAS,CAACM,SAAS,CAAC,CAAC;EACvB;EAEA,IAAIL,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACM,KAAK,CACdC,IAAI,CAAC,EAAE,CAAC,CACRC,OAAO,CAAC,mBAAmB,EAAE,CAACC,EAAE,EAAEC,EAAE,KAAKC,MAAM,CAACC,YAAY,CAACC,QAAQ,CAACH,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EACpF;EAEAI,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,GAAG;QACN,IAAI,CAACV,KAAK,GAAG,EAAE;QACf,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAW,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,CAACZ,KAAK,CAACa,IAAI,CAACD,IAAI,CAAC;EACvB;EAEAE,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG1B,SAAS"}