{"version": 3, "file": "data-validations.js", "names": ["DataValidations", "constructor", "model", "add", "address", "validation", "find", "remove", "undefined", "module", "exports"], "sources": ["../../../lib/doc/data-validations.js"], "sourcesContent": ["class DataValidations {\n  constructor(model) {\n    this.model = model || {};\n  }\n\n  add(address, validation) {\n    return (this.model[address] = validation);\n  }\n\n  find(address) {\n    return this.model[address];\n  }\n\n  remove(address) {\n    this.model[address] = undefined;\n  }\n}\n\nmodule.exports = DataValidations;\n"], "mappings": ";;AAAA,MAAMA,eAAe,CAAC;EACpBC,WAAWA,CAACC,KAAK,EAAE;IACjB,IAAI,CAACA,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;EAC1B;EAEAC,GAAGA,CAACC,OAAO,EAAEC,UAAU,EAAE;IACvB,OAAQ,IAAI,CAACH,KAAK,CAACE,OAAO,CAAC,GAAGC,UAAU;EAC1C;EAEAC,IAAIA,CAACF,OAAO,EAAE;IACZ,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAAC;EAC5B;EAEAG,MAAMA,CAACH,OAAO,EAAE;IACd,IAAI,CAACF,KAAK,CAACE,OAAO,CAAC,GAAGI,SAAS;EACjC;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGV,eAAe"}