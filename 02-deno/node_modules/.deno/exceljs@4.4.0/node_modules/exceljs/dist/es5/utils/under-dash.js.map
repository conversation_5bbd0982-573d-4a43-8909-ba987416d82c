{"version": 3, "file": "under-dash.js", "names": ["toString", "Object", "prototype", "escapeHtmlRegex", "_", "each", "obj", "cb", "Array", "isArray", "for<PERSON>ach", "keys", "key", "some", "every", "map", "keyBy", "a", "p", "reduce", "o", "v", "isEqual", "b", "aType", "bType", "aArray", "b<PERSON><PERSON>y", "length", "aValue", "index", "bValue", "hasOwnProperty", "escapeHtml", "html", "regexResult", "exec", "result", "escape", "lastIndex", "i", "char<PERSON>t", "substring", "strcmp", "isUndefined", "val", "call", "isObject", "deepMerge", "target", "arguments", "src", "clone", "copyIsArray", "assignValue", "module", "exports"], "sources": ["../../../lib/utils/under-dash.js"], "sourcesContent": ["const {toString} = Object.prototype;\nconst escapeHtmlRegex = /[\"&<>]/;\nconst _ = {\n  each: function each(obj, cb) {\n    if (obj) {\n      if (Array.isArray(obj)) {\n        obj.forEach(cb);\n      } else {\n        Object.keys(obj).forEach(key => {\n          cb(obj[key], key);\n        });\n      }\n    }\n  },\n\n  some: function some(obj, cb) {\n    if (obj) {\n      if (Array.isArray(obj)) {\n        return obj.some(cb);\n      }\n      return Object.keys(obj).some(key => cb(obj[key], key));\n    }\n    return false;\n  },\n\n  every: function every(obj, cb) {\n    if (obj) {\n      if (Array.isArray(obj)) {\n        return obj.every(cb);\n      }\n      return Object.keys(obj).every(key => cb(obj[key], key));\n    }\n    return true;\n  },\n\n  map: function map(obj, cb) {\n    if (obj) {\n      if (Array.isArray(obj)) {\n        return obj.map(cb);\n      }\n      return Object.keys(obj).map(key => cb(obj[key], key));\n    }\n    return [];\n  },\n\n  keyBy(a, p) {\n    return a.reduce((o, v) => {\n      o[v[p]] = v;\n      return o;\n    }, {});\n  },\n\n  isEqual: function isEqual(a, b) {\n    const aType = typeof a;\n    const bType = typeof b;\n    const aArray = Array.isArray(a);\n    const bArray = Array.isArray(b);\n    let keys;\n\n    if (aType !== bType) {\n      return false;\n    }\n    switch (typeof a) {\n      case 'object':\n        if (aArray || bArray) {\n          if (aArray && bArray) {\n            return (\n              a.length === b.length &&\n              a.every((aValue, index) => {\n                const bValue = b[index];\n                return _.isEqual(aValue, bValue);\n              })\n            );\n          }\n          return false;\n        }\n\n        if (a === null || b === null) {\n          return a === b;\n        }\n\n        // Compare object keys and values\n        keys = Object.keys(a);\n\n        if (Object.keys(b).length !== keys.length) {\n          return false;\n        }\n\n        for (const key of keys) {\n          if (!b.hasOwnProperty(key)) {\n            return false;\n          }\n        }\n\n        return _.every(a, (aValue, key) => {\n          const bValue = b[key];\n          return _.isEqual(aValue, bValue);\n        });\n\n      default:\n        return a === b;\n    }\n  },\n\n  escapeHtml(html) {\n    const regexResult = escapeHtmlRegex.exec(html);\n    if (!regexResult) return html;\n\n    let result = '';\n    let escape = '';\n    let lastIndex = 0;\n    let i = regexResult.index;\n    for (; i < html.length; i++) {\n      switch (html.charAt(i)) {\n        case '\"':\n          escape = '&quot;';\n          break;\n        case '&':\n          escape = '&amp;';\n          break;\n        case '\\'':\n          escape = '&apos;';\n          break;\n        case '<':\n          escape = '&lt;';\n          break;\n        case '>':\n          escape = '&gt;';\n          break;\n        default:\n          continue;\n      }\n      if (lastIndex !== i) result += html.substring(lastIndex, i);\n      lastIndex = i + 1;\n      result += escape;\n    }\n    if (lastIndex !== i) return result + html.substring(lastIndex, i);\n    return result;\n  },\n\n  strcmp(a, b) {\n    if (a < b) return -1;\n    if (a > b) return 1;\n    return 0;\n  },\n\n  isUndefined(val) {\n    return toString.call(val) === '[object Undefined]';\n  },\n\n  isObject(val) {\n    return toString.call(val) === '[object Object]';\n  },\n\n  deepMerge() {\n    const target = arguments[0] || {};\n    const {length} = arguments;\n    // eslint-disable-next-line one-var\n    let src, clone, copyIsArray;\n\n    function assignValue(val, key) {\n      src = target[key];\n      copyIsArray = Array.isArray(val);\n      if (_.isObject(val) || copyIsArray) {\n        if (copyIsArray) {\n          copyIsArray = false;\n          clone = src && Array.isArray(src) ? src : [];\n        } else {\n          clone = src && _.isObject(src) ? src : {};\n        }\n        target[key] = _.deepMerge(clone, val);\n      } else if (!_.isUndefined(val)) {\n        target[key] = val;\n      }\n    }\n\n    for (let i = 0; i < length; i++) {\n      _.each(arguments[i], assignValue);\n    }\n    return target;\n  },\n};\n\nmodule.exports = _;\n"], "mappings": ";;AAAA,MAAM;EAACA;AAAQ,CAAC,GAAGC,MAAM,CAACC,SAAS;AACnC,MAAMC,eAAe,GAAG,QAAQ;AAChC,MAAMC,CAAC,GAAG;EACRC,IAAI,EAAE,SAASA,IAAIA,CAACC,GAAG,EAAEC,EAAE,EAAE;IAC3B,IAAID,GAAG,EAAE;MACP,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;QACtBA,GAAG,CAACI,OAAO,CAACH,EAAE,CAAC;MACjB,CAAC,MAAM;QACLN,MAAM,CAACU,IAAI,CAACL,GAAG,CAAC,CAACI,OAAO,CAACE,GAAG,IAAI;UAC9BL,EAAE,CAACD,GAAG,CAACM,GAAG,CAAC,EAAEA,GAAG,CAAC;QACnB,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAEDC,IAAI,EAAE,SAASA,IAAIA,CAACP,GAAG,EAAEC,EAAE,EAAE;IAC3B,IAAID,GAAG,EAAE;MACP,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;QACtB,OAAOA,GAAG,CAACO,IAAI,CAACN,EAAE,CAAC;MACrB;MACA,OAAON,MAAM,CAACU,IAAI,CAACL,GAAG,CAAC,CAACO,IAAI,CAACD,GAAG,IAAIL,EAAE,CAACD,GAAG,CAACM,GAAG,CAAC,EAAEA,GAAG,CAAC,CAAC;IACxD;IACA,OAAO,KAAK;EACd,CAAC;EAEDE,KAAK,EAAE,SAASA,KAAKA,CAACR,GAAG,EAAEC,EAAE,EAAE;IAC7B,IAAID,GAAG,EAAE;MACP,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;QACtB,OAAOA,GAAG,CAACQ,KAAK,CAACP,EAAE,CAAC;MACtB;MACA,OAAON,MAAM,CAACU,IAAI,CAACL,GAAG,CAAC,CAACQ,KAAK,CAACF,GAAG,IAAIL,EAAE,CAACD,GAAG,CAACM,GAAG,CAAC,EAAEA,GAAG,CAAC,CAAC;IACzD;IACA,OAAO,IAAI;EACb,CAAC;EAEDG,GAAG,EAAE,SAASA,GAAGA,CAACT,GAAG,EAAEC,EAAE,EAAE;IACzB,IAAID,GAAG,EAAE;MACP,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;QACtB,OAAOA,GAAG,CAACS,GAAG,CAACR,EAAE,CAAC;MACpB;MACA,OAAON,MAAM,CAACU,IAAI,CAACL,GAAG,CAAC,CAACS,GAAG,CAACH,GAAG,IAAIL,EAAE,CAACD,GAAG,CAACM,GAAG,CAAC,EAAEA,GAAG,CAAC,CAAC;IACvD;IACA,OAAO,EAAE;EACX,CAAC;EAEDI,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACV,OAAOD,CAAC,CAACE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACxBD,CAAC,CAACC,CAAC,CAACH,CAAC,CAAC,CAAC,GAAGG,CAAC;MACX,OAAOD,CAAC;IACV,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC;EAEDE,OAAO,EAAE,SAASA,OAAOA,CAACL,CAAC,EAAEM,CAAC,EAAE;IAC9B,MAAMC,KAAK,GAAG,OAAOP,CAAC;IACtB,MAAMQ,KAAK,GAAG,OAAOF,CAAC;IACtB,MAAMG,MAAM,GAAGlB,KAAK,CAACC,OAAO,CAACQ,CAAC,CAAC;IAC/B,MAAMU,MAAM,GAAGnB,KAAK,CAACC,OAAO,CAACc,CAAC,CAAC;IAC/B,IAAIZ,IAAI;IAER,IAAIa,KAAK,KAAKC,KAAK,EAAE;MACnB,OAAO,KAAK;IACd;IACA,QAAQ,OAAOR,CAAC;MACd,KAAK,QAAQ;QACX,IAAIS,MAAM,IAAIC,MAAM,EAAE;UACpB,IAAID,MAAM,IAAIC,MAAM,EAAE;YACpB,OACEV,CAAC,CAACW,MAAM,KAAKL,CAAC,CAACK,MAAM,IACrBX,CAAC,CAACH,KAAK,CAAC,CAACe,MAAM,EAAEC,KAAK,KAAK;cACzB,MAAMC,MAAM,GAAGR,CAAC,CAACO,KAAK,CAAC;cACvB,OAAO1B,CAAC,CAACkB,OAAO,CAACO,MAAM,EAAEE,MAAM,CAAC;YAClC,CAAC,CAAC;UAEN;UACA,OAAO,KAAK;QACd;QAEA,IAAId,CAAC,KAAK,IAAI,IAAIM,CAAC,KAAK,IAAI,EAAE;UAC5B,OAAON,CAAC,KAAKM,CAAC;QAChB;;QAEA;QACAZ,IAAI,GAAGV,MAAM,CAACU,IAAI,CAACM,CAAC,CAAC;QAErB,IAAIhB,MAAM,CAACU,IAAI,CAACY,CAAC,CAAC,CAACK,MAAM,KAAKjB,IAAI,CAACiB,MAAM,EAAE;UACzC,OAAO,KAAK;QACd;QAEA,KAAK,MAAMhB,GAAG,IAAID,IAAI,EAAE;UACtB,IAAI,CAACY,CAAC,CAACS,cAAc,CAACpB,GAAG,CAAC,EAAE;YAC1B,OAAO,KAAK;UACd;QACF;QAEA,OAAOR,CAAC,CAACU,KAAK,CAACG,CAAC,EAAE,CAACY,MAAM,EAAEjB,GAAG,KAAK;UACjC,MAAMmB,MAAM,GAAGR,CAAC,CAACX,GAAG,CAAC;UACrB,OAAOR,CAAC,CAACkB,OAAO,CAACO,MAAM,EAAEE,MAAM,CAAC;QAClC,CAAC,CAAC;MAEJ;QACE,OAAOd,CAAC,KAAKM,CAAC;IAClB;EACF,CAAC;EAEDU,UAAUA,CAACC,IAAI,EAAE;IACf,MAAMC,WAAW,GAAGhC,eAAe,CAACiC,IAAI,CAACF,IAAI,CAAC;IAC9C,IAAI,CAACC,WAAW,EAAE,OAAOD,IAAI;IAE7B,IAAIG,MAAM,GAAG,EAAE;IACf,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,CAAC,GAAGL,WAAW,CAACL,KAAK;IACzB,OAAOU,CAAC,GAAGN,IAAI,CAACN,MAAM,EAAEY,CAAC,EAAE,EAAE;MAC3B,QAAQN,IAAI,CAACO,MAAM,CAACD,CAAC,CAAC;QACpB,KAAK,GAAG;UACNF,MAAM,GAAG,QAAQ;UACjB;QACF,KAAK,GAAG;UACNA,MAAM,GAAG,OAAO;UAChB;QACF,KAAK,IAAI;UACPA,MAAM,GAAG,QAAQ;UACjB;QACF,KAAK,GAAG;UACNA,MAAM,GAAG,MAAM;UACf;QACF,KAAK,GAAG;UACNA,MAAM,GAAG,MAAM;UACf;QACF;UACE;MACJ;MACA,IAAIC,SAAS,KAAKC,CAAC,EAAEH,MAAM,IAAIH,IAAI,CAACQ,SAAS,CAACH,SAAS,EAAEC,CAAC,CAAC;MAC3DD,SAAS,GAAGC,CAAC,GAAG,CAAC;MACjBH,MAAM,IAAIC,MAAM;IAClB;IACA,IAAIC,SAAS,KAAKC,CAAC,EAAE,OAAOH,MAAM,GAAGH,IAAI,CAACQ,SAAS,CAACH,SAAS,EAAEC,CAAC,CAAC;IACjE,OAAOH,MAAM;EACf,CAAC;EAEDM,MAAMA,CAAC1B,CAAC,EAAEM,CAAC,EAAE;IACX,IAAIN,CAAC,GAAGM,CAAC,EAAE,OAAO,CAAC,CAAC;IACpB,IAAIN,CAAC,GAAGM,CAAC,EAAE,OAAO,CAAC;IACnB,OAAO,CAAC;EACV,CAAC;EAEDqB,WAAWA,CAACC,GAAG,EAAE;IACf,OAAO7C,QAAQ,CAAC8C,IAAI,CAACD,GAAG,CAAC,KAAK,oBAAoB;EACpD,CAAC;EAEDE,QAAQA,CAACF,GAAG,EAAE;IACZ,OAAO7C,QAAQ,CAAC8C,IAAI,CAACD,GAAG,CAAC,KAAK,iBAAiB;EACjD,CAAC;EAEDG,SAASA,CAAA,EAAG;IACV,MAAMC,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACjC,MAAM;MAACtB;IAAM,CAAC,GAAGsB,SAAS;IAC1B;IACA,IAAIC,GAAG,EAAEC,KAAK,EAAEC,WAAW;IAE3B,SAASC,WAAWA,CAACT,GAAG,EAAEjC,GAAG,EAAE;MAC7BuC,GAAG,GAAGF,MAAM,CAACrC,GAAG,CAAC;MACjByC,WAAW,GAAG7C,KAAK,CAACC,OAAO,CAACoC,GAAG,CAAC;MAChC,IAAIzC,CAAC,CAAC2C,QAAQ,CAACF,GAAG,CAAC,IAAIQ,WAAW,EAAE;QAClC,IAAIA,WAAW,EAAE;UACfA,WAAW,GAAG,KAAK;UACnBD,KAAK,GAAGD,GAAG,IAAI3C,KAAK,CAACC,OAAO,CAAC0C,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;QAC9C,CAAC,MAAM;UACLC,KAAK,GAAGD,GAAG,IAAI/C,CAAC,CAAC2C,QAAQ,CAACI,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC;QAC3C;QACAF,MAAM,CAACrC,GAAG,CAAC,GAAGR,CAAC,CAAC4C,SAAS,CAACI,KAAK,EAAEP,GAAG,CAAC;MACvC,CAAC,MAAM,IAAI,CAACzC,CAAC,CAACwC,WAAW,CAACC,GAAG,CAAC,EAAE;QAC9BI,MAAM,CAACrC,GAAG,CAAC,GAAGiC,GAAG;MACnB;IACF;IAEA,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,MAAM,EAAEY,CAAC,EAAE,EAAE;MAC/BpC,CAAC,CAACC,IAAI,CAAC6C,SAAS,CAACV,CAAC,CAAC,EAAEc,WAAW,CAAC;IACnC;IACA,OAAOL,MAAM;EACf;AACF,CAAC;AAEDM,MAAM,CAACC,OAAO,GAAGpD,CAAC"}