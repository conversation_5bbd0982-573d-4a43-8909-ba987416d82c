{"version": 3, "file": "comment-xform.js", "names": ["RichTextXform", "require", "utils", "BaseXform", "CommentXform", "module", "exports", "model", "inherits", "tag", "richTextXform", "_richTextXform", "render", "xmlStream", "openNode", "ref", "authorId", "note", "texts", "for<PERSON>ach", "text", "closeNode", "parseOpen", "node", "parser", "name", "type", "attributes", "parseText", "parseClose", "push", "undefined"], "sources": ["../../../../../lib/xlsx/xform/comment/comment-xform.js"], "sourcesContent": ["const RichTextXform = require('../strings/rich-text-xform');\nconst utils = require('../../../utils/utils');\nconst BaseXform = require('../base-xform');\n\n/**\n  <comment ref=\"B1\" authorId=\"0\">\n    <text>\n      <r>\n        <rPr>\n          <b/>\n          <sz val=\"9\"/>\n          <rFont val=\"宋体\"/>\n          <charset val=\"134\"/>\n        </rPr>\n        <t>51422:</t>\n      </r>\n      <r>\n        <rPr>\n          <sz val=\"9\"/>\n          <rFont val=\"宋体\"/>\n          <charset val=\"134\"/>\n        </rPr>\n        <t xml:space=\"preserve\">&#10;test</t>\n      </r>\n    </text>\n  </comment>\n */\n\nconst CommentXform = (module.exports = function(model) {\n  this.model = model;\n});\n\nutils.inherits(CommentXform, BaseXform, {\n  get tag() {\n    return 'r';\n  },\n\n  get richTextXform() {\n    if (!this._richTextXform) {\n      this._richTextXform = new RichTextXform();\n    }\n    return this._richTextXform;\n  },\n\n  render(xmlStream, model) {\n    model = model || this.model;\n\n    xmlStream.openNode('comment', {\n      ref: model.ref,\n      authorId: 0,\n    });\n    xmlStream.openNode('text');\n    if (model && model.note && model.note.texts) {\n      model.note.texts.forEach(text => {\n        this.richTextXform.render(xmlStream, text);\n      });\n    }\n    xmlStream.closeNode();\n    xmlStream.closeNode();\n  },\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'comment':\n        this.model = {\n          type: 'note',\n          note: {\n            texts: [],\n          },\n          ...node.attributes,\n        };\n        return true;\n      case 'r':\n        this.parser = this.richTextXform;\n        this.parser.parseOpen(node);\n        return true;\n      default:\n        return false;\n    }\n  },\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  },\n  parseClose(name) {\n    switch (name) {\n      case 'comment':\n        return false;\n      case 'r':\n        this.model.note.texts.push(this.parser.model);\n        this.parser = undefined;\n        return true;\n      default:\n        if (this.parser) {\n          this.parser.parseClose(name);\n        }\n        return true;\n    }\n  },\n});\n"], "mappings": ";;AAAA,MAAMA,aAAa,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAC3D,MAAMC,KAAK,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC7C,MAAME,SAAS,GAAGF,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMG,YAAY,GAAIC,MAAM,CAACC,OAAO,GAAG,UAASC,KAAK,EAAE;EACrD,IAAI,CAACA,KAAK,GAAGA,KAAK;AACpB,CAAE;AAEFL,KAAK,CAACM,QAAQ,CAACJ,YAAY,EAAED,SAAS,EAAE;EACtC,IAAIM,GAAGA,CAAA,EAAG;IACR,OAAO,GAAG;EACZ,CAAC;EAED,IAAIC,aAAaA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;MACxB,IAAI,CAACA,cAAc,GAAG,IAAIX,aAAa,CAAC,CAAC;IAC3C;IACA,OAAO,IAAI,CAACW,cAAc;EAC5B,CAAC;EAEDC,MAAMA,CAACC,SAAS,EAAEN,KAAK,EAAE;IACvBA,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACA,KAAK;IAE3BM,SAAS,CAACC,QAAQ,CAAC,SAAS,EAAE;MAC5BC,GAAG,EAAER,KAAK,CAACQ,GAAG;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFH,SAAS,CAACC,QAAQ,CAAC,MAAM,CAAC;IAC1B,IAAIP,KAAK,IAAIA,KAAK,CAACU,IAAI,IAAIV,KAAK,CAACU,IAAI,CAACC,KAAK,EAAE;MAC3CX,KAAK,CAACU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACC,IAAI,IAAI;QAC/B,IAAI,CAACV,aAAa,CAACE,MAAM,CAACC,SAAS,EAAEO,IAAI,CAAC;MAC5C,CAAC,CAAC;IACJ;IACAP,SAAS,CAACQ,SAAS,CAAC,CAAC;IACrBR,SAAS,CAACQ,SAAS,CAAC,CAAC;EACvB,CAAC;EAEDC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,SAAS;QACZ,IAAI,CAAClB,KAAK,GAAG;UACXmB,IAAI,EAAE,MAAM;UACZT,IAAI,EAAE;YACJC,KAAK,EAAE;UACT,CAAC;UACD,GAAGK,IAAI,CAACI;QACV,CAAC;QACD,OAAO,IAAI;MACb,KAAK,GAAG;QACN,IAAI,CAACH,MAAM,GAAG,IAAI,CAACd,aAAa;QAChC,IAAI,CAACc,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF,CAAC;EACDK,SAASA,CAACR,IAAI,EAAE;IACd,IAAI,IAAI,CAACI,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACI,SAAS,CAACR,IAAI,CAAC;IAC7B;EACF,CAAC;EACDS,UAAUA,CAACJ,IAAI,EAAE;IACf,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,GAAG;QACN,IAAI,CAAClB,KAAK,CAACU,IAAI,CAACC,KAAK,CAACY,IAAI,CAAC,IAAI,CAACN,MAAM,CAACjB,KAAK,CAAC;QAC7C,IAAI,CAACiB,MAAM,GAAGO,SAAS;QACvB,OAAO,IAAI;MACb;QACE,IAAI,IAAI,CAACP,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACK,UAAU,CAACJ,IAAI,CAAC;QAC9B;QACA,OAAO,IAAI;IACf;EACF;AACF,CAAC,CAAC"}