{"version": 3, "file": "border-xform.js", "names": ["BaseXform", "require", "utils", "ColorXform", "EdgeXform", "constructor", "name", "map", "color", "tag", "render", "xmlStream", "model", "defaultColor", "openNode", "style", "addAttribute", "closeNode", "parseOpen", "node", "parser", "attributes", "undefined", "parseText", "text", "parseClose", "validStyle", "value", "validStyleValues", "reduce", "p", "v", "BorderXform", "top", "left", "bottom", "right", "diagonal", "up", "down", "add", "edgeModel", "edgeXform", "reset", "diagonalUp", "parseBoolean", "diagonalDown", "key", "extensions", "Object", "assign", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/border-xform.js"], "sourcesContent": ["/* eslint-disable max-classes-per-file */\nconst BaseXform = require('../base-xform');\nconst utils = require('../../../utils/utils');\n\nconst ColorXform = require('./color-xform');\n\nclass EdgeXform extends BaseXform {\n  constructor(name) {\n    super();\n\n    this.name = name;\n    this.map = {\n      color: new ColorXform(),\n    };\n  }\n\n  get tag() {\n    return this.name;\n  }\n\n  render(xmlStream, model, defaultColor) {\n    const color = (model && model.color) || defaultColor || this.defaultColor;\n    xmlStream.openNode(this.name);\n    if (model && model.style) {\n      xmlStream.addAttribute('style', model.style);\n      if (color) {\n        this.map.color.render(xmlStream, color);\n      }\n    }\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case this.name: {\n        const {style} = node.attributes;\n        if (style) {\n          this.model = {\n            style,\n          };\n        } else {\n          this.model = undefined;\n        }\n        return true;\n      }\n      case 'color':\n        this.parser = this.map.color;\n        this.parser.parseOpen(node);\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n\n    if (name === this.name) {\n      if (this.map.color.model) {\n        if (!this.model) {\n          this.model = {};\n        }\n        this.model.color = this.map.color.model;\n      }\n    }\n\n    return false;\n  }\n\n  validStyle(value) {\n    return EdgeXform.validStyleValues[value];\n  }\n}\n\nEdgeXform.validStyleValues = [\n  'thin',\n  'dashed',\n  'dotted',\n  'dashDot',\n  'hair',\n  'dashDotDot',\n  'slantDashDot',\n  'mediumDashed',\n  'mediumDashDotDot',\n  'mediumDashDot',\n  'medium',\n  'double',\n  'thick',\n].reduce((p, v) => {\n  p[v] = true;\n  return p;\n}, {});\n\n// Border encapsulates translation from border model to/from xlsx\nclass BorderXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      top: new EdgeXform('top'),\n      left: new EdgeXform('left'),\n      bottom: new EdgeXform('bottom'),\n      right: new EdgeXform('right'),\n      diagonal: new EdgeXform('diagonal'),\n    };\n  }\n\n  render(xmlStream, model) {\n    const {color} = model;\n    xmlStream.openNode('border');\n    if (model.diagonal && model.diagonal.style) {\n      if (model.diagonal.up) {\n        xmlStream.addAttribute('diagonalUp', '1');\n      }\n      if (model.diagonal.down) {\n        xmlStream.addAttribute('diagonalDown', '1');\n      }\n    }\n    function add(edgeModel, edgeXform) {\n      if (edgeModel && !edgeModel.color && model.color) {\n        // don't mess with incoming models\n        edgeModel = {\n          ...edgeModel,\n          color: model.color,\n        };\n      }\n      edgeXform.render(xmlStream, edgeModel, color);\n    }\n    add(model.left, this.map.left);\n    add(model.right, this.map.right);\n    add(model.top, this.map.top);\n    add(model.bottom, this.map.bottom);\n    add(model.diagonal, this.map.diagonal);\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'border':\n        this.reset();\n        this.diagonalUp = utils.parseBoolean(node.attributes.diagonalUp);\n        this.diagonalDown = utils.parseBoolean(node.attributes.diagonalDown);\n        return true;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n          return true;\n        }\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    if (name === 'border') {\n      const model = (this.model = {});\n      const add = function(key, edgeModel, extensions) {\n        if (edgeModel) {\n          if (extensions) {\n            Object.assign(edgeModel, extensions);\n          }\n          model[key] = edgeModel;\n        }\n      };\n      add('left', this.map.left.model);\n      add('right', this.map.right.model);\n      add('top', this.map.top.model);\n      add('bottom', this.map.bottom.model);\n      add('diagonal', this.map.diagonal.model, {up: this.diagonalUp, down: this.diagonalDown});\n    }\n    return false;\n  }\n}\n\nmodule.exports = BorderXform;\n"], "mappings": ";;AAAA;AACA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,KAAK,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAE7C,MAAME,UAAU,GAAGF,OAAO,CAAC,eAAe,CAAC;AAE3C,MAAMG,SAAS,SAASJ,SAAS,CAAC;EAChCK,WAAWA,CAACC,IAAI,EAAE;IAChB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAG;MACTC,KAAK,EAAE,IAAIL,UAAU,CAAC;IACxB,CAAC;EACH;EAEA,IAAIM,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACH,IAAI;EAClB;EAEAI,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAE;IACrC,MAAML,KAAK,GAAII,KAAK,IAAIA,KAAK,CAACJ,KAAK,IAAKK,YAAY,IAAI,IAAI,CAACA,YAAY;IACzEF,SAAS,CAACG,QAAQ,CAAC,IAAI,CAACR,IAAI,CAAC;IAC7B,IAAIM,KAAK,IAAIA,KAAK,CAACG,KAAK,EAAE;MACxBJ,SAAS,CAACK,YAAY,CAAC,OAAO,EAAEJ,KAAK,CAACG,KAAK,CAAC;MAC5C,IAAIP,KAAK,EAAE;QACT,IAAI,CAACD,GAAG,CAACC,KAAK,CAACE,MAAM,CAACC,SAAS,EAAEH,KAAK,CAAC;MACzC;IACF;IACAG,SAAS,CAACM,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACb,IAAI;MACf,KAAK,IAAI,CAACA,IAAI;QAAE;UACd,MAAM;YAACS;UAAK,CAAC,GAAGI,IAAI,CAACE,UAAU;UAC/B,IAAIN,KAAK,EAAE;YACT,IAAI,CAACH,KAAK,GAAG;cACXG;YACF,CAAC;UACH,CAAC,MAAM;YACL,IAAI,CAACH,KAAK,GAAGU,SAAS;UACxB;UACA,OAAO,IAAI;QACb;MACA,KAAK,OAAO;QACV,IAAI,CAACF,MAAM,GAAG,IAAI,CAACb,GAAG,CAACC,KAAK;QAC5B,IAAI,CAACY,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAI,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACJ,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACG,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACnB,IAAI,EAAE;IACf,IAAI,IAAI,CAACc,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACnB,IAAI,CAAC,EAAE;QACjC,IAAI,CAACc,MAAM,GAAGE,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IAEA,IAAIhB,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;MACtB,IAAI,IAAI,CAACC,GAAG,CAACC,KAAK,CAACI,KAAK,EAAE;QACxB,IAAI,CAAC,IAAI,CAACA,KAAK,EAAE;UACf,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC;QACjB;QACA,IAAI,CAACA,KAAK,CAACJ,KAAK,GAAG,IAAI,CAACD,GAAG,CAACC,KAAK,CAACI,KAAK;MACzC;IACF;IAEA,OAAO,KAAK;EACd;EAEAc,UAAUA,CAACC,KAAK,EAAE;IAChB,OAAOvB,SAAS,CAACwB,gBAAgB,CAACD,KAAK,CAAC;EAC1C;AACF;AAEAvB,SAAS,CAACwB,gBAAgB,GAAG,CAC3B,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,MAAM,EACN,YAAY,EACZ,cAAc,EACd,cAAc,EACd,kBAAkB,EAClB,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,OAAO,CACR,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EACjBD,CAAC,CAACC,CAAC,CAAC,GAAG,IAAI;EACX,OAAOD,CAAC;AACV,CAAC,EAAE,CAAC,CAAC,CAAC;;AAEN;AACA,MAAME,WAAW,SAAShC,SAAS,CAAC;EAClCK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACE,GAAG,GAAG;MACT0B,GAAG,EAAE,IAAI7B,SAAS,CAAC,KAAK,CAAC;MACzB8B,IAAI,EAAE,IAAI9B,SAAS,CAAC,MAAM,CAAC;MAC3B+B,MAAM,EAAE,IAAI/B,SAAS,CAAC,QAAQ,CAAC;MAC/BgC,KAAK,EAAE,IAAIhC,SAAS,CAAC,OAAO,CAAC;MAC7BiC,QAAQ,EAAE,IAAIjC,SAAS,CAAC,UAAU;IACpC,CAAC;EACH;EAEAM,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,MAAM;MAACJ;IAAK,CAAC,GAAGI,KAAK;IACrBD,SAAS,CAACG,QAAQ,CAAC,QAAQ,CAAC;IAC5B,IAAIF,KAAK,CAACyB,QAAQ,IAAIzB,KAAK,CAACyB,QAAQ,CAACtB,KAAK,EAAE;MAC1C,IAAIH,KAAK,CAACyB,QAAQ,CAACC,EAAE,EAAE;QACrB3B,SAAS,CAACK,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;MAC3C;MACA,IAAIJ,KAAK,CAACyB,QAAQ,CAACE,IAAI,EAAE;QACvB5B,SAAS,CAACK,YAAY,CAAC,cAAc,EAAE,GAAG,CAAC;MAC7C;IACF;IACA,SAASwB,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAE;MACjC,IAAID,SAAS,IAAI,CAACA,SAAS,CAACjC,KAAK,IAAII,KAAK,CAACJ,KAAK,EAAE;QAChD;QACAiC,SAAS,GAAG;UACV,GAAGA,SAAS;UACZjC,KAAK,EAAEI,KAAK,CAACJ;QACf,CAAC;MACH;MACAkC,SAAS,CAAChC,MAAM,CAACC,SAAS,EAAE8B,SAAS,EAAEjC,KAAK,CAAC;IAC/C;IACAgC,GAAG,CAAC5B,KAAK,CAACsB,IAAI,EAAE,IAAI,CAAC3B,GAAG,CAAC2B,IAAI,CAAC;IAC9BM,GAAG,CAAC5B,KAAK,CAACwB,KAAK,EAAE,IAAI,CAAC7B,GAAG,CAAC6B,KAAK,CAAC;IAChCI,GAAG,CAAC5B,KAAK,CAACqB,GAAG,EAAE,IAAI,CAAC1B,GAAG,CAAC0B,GAAG,CAAC;IAC5BO,GAAG,CAAC5B,KAAK,CAACuB,MAAM,EAAE,IAAI,CAAC5B,GAAG,CAAC4B,MAAM,CAAC;IAClCK,GAAG,CAAC5B,KAAK,CAACyB,QAAQ,EAAE,IAAI,CAAC9B,GAAG,CAAC8B,QAAQ,CAAC;IAEtC1B,SAAS,CAACM,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACb,IAAI;MACf,KAAK,QAAQ;QACX,IAAI,CAACqC,KAAK,CAAC,CAAC;QACZ,IAAI,CAACC,UAAU,GAAG1C,KAAK,CAAC2C,YAAY,CAAC1B,IAAI,CAACE,UAAU,CAACuB,UAAU,CAAC;QAChE,IAAI,CAACE,YAAY,GAAG5C,KAAK,CAAC2C,YAAY,CAAC1B,IAAI,CAACE,UAAU,CAACyB,YAAY,CAAC;QACpE,OAAO,IAAI;MACb;QACE,IAAI,CAAC1B,MAAM,GAAG,IAAI,CAACb,GAAG,CAACY,IAAI,CAACb,IAAI,CAAC;QACjC,IAAI,IAAI,CAACc,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;UAC3B,OAAO,IAAI;QACb;QACA,OAAO,KAAK;IAChB;EACF;EAEAI,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACJ,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACG,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACnB,IAAI,EAAE;IACf,IAAI,IAAI,CAACc,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACnB,IAAI,CAAC,EAAE;QACjC,IAAI,CAACc,MAAM,GAAGE,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,IAAIhB,IAAI,KAAK,QAAQ,EAAE;MACrB,MAAMM,KAAK,GAAI,IAAI,CAACA,KAAK,GAAG,CAAC,CAAE;MAC/B,MAAM4B,GAAG,GAAG,SAAAA,CAASO,GAAG,EAAEN,SAAS,EAAEO,UAAU,EAAE;QAC/C,IAAIP,SAAS,EAAE;UACb,IAAIO,UAAU,EAAE;YACdC,MAAM,CAACC,MAAM,CAACT,SAAS,EAAEO,UAAU,CAAC;UACtC;UACApC,KAAK,CAACmC,GAAG,CAAC,GAAGN,SAAS;QACxB;MACF,CAAC;MACDD,GAAG,CAAC,MAAM,EAAE,IAAI,CAACjC,GAAG,CAAC2B,IAAI,CAACtB,KAAK,CAAC;MAChC4B,GAAG,CAAC,OAAO,EAAE,IAAI,CAACjC,GAAG,CAAC6B,KAAK,CAACxB,KAAK,CAAC;MAClC4B,GAAG,CAAC,KAAK,EAAE,IAAI,CAACjC,GAAG,CAAC0B,GAAG,CAACrB,KAAK,CAAC;MAC9B4B,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACjC,GAAG,CAAC4B,MAAM,CAACvB,KAAK,CAAC;MACpC4B,GAAG,CAAC,UAAU,EAAE,IAAI,CAACjC,GAAG,CAAC8B,QAAQ,CAACzB,KAAK,EAAE;QAAC0B,EAAE,EAAE,IAAI,CAACM,UAAU;QAAEL,IAAI,EAAE,IAAI,CAACO;MAAY,CAAC,CAAC;IAC1F;IACA,OAAO,KAAK;EACd;AACF;AAEAK,MAAM,CAACC,OAAO,GAAGpB,WAAW"}