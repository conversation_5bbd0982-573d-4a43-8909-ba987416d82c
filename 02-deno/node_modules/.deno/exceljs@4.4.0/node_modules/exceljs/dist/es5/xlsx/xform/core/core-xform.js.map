{"version": 3, "file": "core-xform.js", "names": ["XmlStream", "require", "BaseXform", "DateXform", "StringXform", "IntegerXform", "CoreXform", "constructor", "map", "tag", "format", "DateFormat", "attrs", "DateAttrs", "render", "xmlStream", "model", "openXml", "StdDocAttributes", "openNode", "CORE_PROPERTY_ATTRIBUTES", "creator", "title", "subject", "description", "identifier", "language", "keywords", "category", "lastModifiedBy", "lastPrinted", "revision", "version", "contentStatus", "contentType", "created", "modified", "closeNode", "parseOpen", "node", "parser", "name", "Error", "JSON", "stringify", "parseText", "text", "parseClose", "undefined", "dt", "toISOString", "replace", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/core/core-xform.js"], "sourcesContent": ["const XmlStream = require('../../../utils/xml-stream');\nconst BaseXform = require('../base-xform');\nconst DateXform = require('../simple/date-xform');\nconst StringXform = require('../simple/string-xform');\nconst IntegerXform = require('../simple/integer-xform');\n\nclass CoreXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'dc:creator': new StringXform({tag: 'dc:creator'}),\n      'dc:title': new StringXform({tag: 'dc:title'}),\n      'dc:subject': new StringXform({tag: 'dc:subject'}),\n      'dc:description': new StringXform({tag: 'dc:description'}),\n      'dc:identifier': new StringXform({tag: 'dc:identifier'}),\n      'dc:language': new StringXform({tag: 'dc:language'}),\n      'cp:keywords': new StringXform({tag: 'cp:keywords'}),\n      'cp:category': new StringXform({tag: 'cp:category'}),\n      'cp:lastModifiedBy': new StringXform({tag: 'cp:lastModifiedBy'}),\n      'cp:lastPrinted': new DateXform({tag: 'cp:lastPrinted', format: CoreXform.DateFormat}),\n      'cp:revision': new IntegerXform({tag: 'cp:revision'}),\n      'cp:version': new StringXform({tag: 'cp:version'}),\n      'cp:contentStatus': new StringXform({tag: 'cp:contentStatus'}),\n      'cp:contentType': new StringXform({tag: 'cp:contentType'}),\n      'dcterms:created': new DateXform({\n        tag: 'dcterms:created',\n        attrs: CoreXform.DateAttrs,\n        format: CoreXform.DateFormat,\n      }),\n      'dcterms:modified': new DateXform({\n        tag: 'dcterms:modified',\n        attrs: CoreXform.DateAttrs,\n        format: CoreXform.DateFormat,\n      }),\n    };\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n\n    xmlStream.openNode('cp:coreProperties', CoreXform.CORE_PROPERTY_ATTRIBUTES);\n\n    this.map['dc:creator'].render(xmlStream, model.creator);\n    this.map['dc:title'].render(xmlStream, model.title);\n    this.map['dc:subject'].render(xmlStream, model.subject);\n    this.map['dc:description'].render(xmlStream, model.description);\n    this.map['dc:identifier'].render(xmlStream, model.identifier);\n    this.map['dc:language'].render(xmlStream, model.language);\n    this.map['cp:keywords'].render(xmlStream, model.keywords);\n    this.map['cp:category'].render(xmlStream, model.category);\n    this.map['cp:lastModifiedBy'].render(xmlStream, model.lastModifiedBy);\n    this.map['cp:lastPrinted'].render(xmlStream, model.lastPrinted);\n    this.map['cp:revision'].render(xmlStream, model.revision);\n    this.map['cp:version'].render(xmlStream, model.version);\n    this.map['cp:contentStatus'].render(xmlStream, model.contentStatus);\n    this.map['cp:contentType'].render(xmlStream, model.contentType);\n    this.map['dcterms:created'].render(xmlStream, model.created);\n    this.map['dcterms:modified'].render(xmlStream, model.modified);\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'cp:coreProperties':\n      case 'coreProperties':\n        return true;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n          return true;\n        }\n        throw new Error(`Unexpected xml node in parseOpen: ${JSON.stringify(node)}`);\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case 'cp:coreProperties':\n      case 'coreProperties':\n        this.model = {\n          creator: this.map['dc:creator'].model,\n          title: this.map['dc:title'].model,\n          subject: this.map['dc:subject'].model,\n          description: this.map['dc:description'].model,\n          identifier: this.map['dc:identifier'].model,\n          language: this.map['dc:language'].model,\n          keywords: this.map['cp:keywords'].model,\n          category: this.map['cp:category'].model,\n          lastModifiedBy: this.map['cp:lastModifiedBy'].model,\n          lastPrinted: this.map['cp:lastPrinted'].model,\n          revision: this.map['cp:revision'].model,\n          contentStatus: this.map['cp:contentStatus'].model,\n          contentType: this.map['cp:contentType'].model,\n          created: this.map['dcterms:created'].model,\n          modified: this.map['dcterms:modified'].model,\n        };\n        return false;\n      default:\n        throw new Error(`Unexpected xml node in parseClose: ${name}`);\n    }\n  }\n}\n\nCoreXform.DateFormat = function(dt) {\n  return dt.toISOString().replace(/[.]\\d{3}/, '');\n};\nCoreXform.DateAttrs = {'xsi:type': 'dcterms:W3CDTF'};\n\nCoreXform.CORE_PROPERTY_ATTRIBUTES = {\n  'xmlns:cp': 'http://schemas.openxmlformats.org/package/2006/metadata/core-properties',\n  'xmlns:dc': 'http://purl.org/dc/elements/1.1/',\n  'xmlns:dcterms': 'http://purl.org/dc/terms/',\n  'xmlns:dcmitype': 'http://purl.org/dc/dcmitype/',\n  'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',\n};\n\nmodule.exports = CoreXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACtD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAME,SAAS,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AACjD,MAAMG,WAAW,GAAGH,OAAO,CAAC,wBAAwB,CAAC;AACrD,MAAMI,YAAY,GAAGJ,OAAO,CAAC,yBAAyB,CAAC;AAEvD,MAAMK,SAAS,SAASJ,SAAS,CAAC;EAChCK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,YAAY,EAAE,IAAIJ,WAAW,CAAC;QAACK,GAAG,EAAE;MAAY,CAAC,CAAC;MAClD,UAAU,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAU,CAAC,CAAC;MAC9C,YAAY,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAY,CAAC,CAAC;MAClD,gBAAgB,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAgB,CAAC,CAAC;MAC1D,eAAe,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAe,CAAC,CAAC;MACxD,aAAa,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAa,CAAC,CAAC;MACpD,aAAa,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAa,CAAC,CAAC;MACpD,aAAa,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAa,CAAC,CAAC;MACpD,mBAAmB,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAmB,CAAC,CAAC;MAChE,gBAAgB,EAAE,IAAIN,SAAS,CAAC;QAACM,GAAG,EAAE,gBAAgB;QAAEC,MAAM,EAAEJ,SAAS,CAACK;MAAU,CAAC,CAAC;MACtF,aAAa,EAAE,IAAIN,YAAY,CAAC;QAACI,GAAG,EAAE;MAAa,CAAC,CAAC;MACrD,YAAY,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAY,CAAC,CAAC;MAClD,kBAAkB,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAkB,CAAC,CAAC;MAC9D,gBAAgB,EAAE,IAAIL,WAAW,CAAC;QAACK,GAAG,EAAE;MAAgB,CAAC,CAAC;MAC1D,iBAAiB,EAAE,IAAIN,SAAS,CAAC;QAC/BM,GAAG,EAAE,iBAAiB;QACtBG,KAAK,EAAEN,SAAS,CAACO,SAAS;QAC1BH,MAAM,EAAEJ,SAAS,CAACK;MACpB,CAAC,CAAC;MACF,kBAAkB,EAAE,IAAIR,SAAS,CAAC;QAChCM,GAAG,EAAE,kBAAkB;QACvBG,KAAK,EAAEN,SAAS,CAACO,SAAS;QAC1BH,MAAM,EAAEJ,SAAS,CAACK;MACpB,CAAC;IACH,CAAC;EACH;EAEAG,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,OAAO,CAACjB,SAAS,CAACkB,gBAAgB,CAAC;IAE7CH,SAAS,CAACI,QAAQ,CAAC,mBAAmB,EAAEb,SAAS,CAACc,wBAAwB,CAAC;IAE3E,IAAI,CAACZ,GAAG,CAAC,YAAY,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACK,OAAO,CAAC;IACvD,IAAI,CAACb,GAAG,CAAC,UAAU,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACM,KAAK,CAAC;IACnD,IAAI,CAACd,GAAG,CAAC,YAAY,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACO,OAAO,CAAC;IACvD,IAAI,CAACf,GAAG,CAAC,gBAAgB,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACQ,WAAW,CAAC;IAC/D,IAAI,CAAChB,GAAG,CAAC,eAAe,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACS,UAAU,CAAC;IAC7D,IAAI,CAACjB,GAAG,CAAC,aAAa,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACU,QAAQ,CAAC;IACzD,IAAI,CAAClB,GAAG,CAAC,aAAa,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACW,QAAQ,CAAC;IACzD,IAAI,CAACnB,GAAG,CAAC,aAAa,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACY,QAAQ,CAAC;IACzD,IAAI,CAACpB,GAAG,CAAC,mBAAmB,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACa,cAAc,CAAC;IACrE,IAAI,CAACrB,GAAG,CAAC,gBAAgB,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACc,WAAW,CAAC;IAC/D,IAAI,CAACtB,GAAG,CAAC,aAAa,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACe,QAAQ,CAAC;IACzD,IAAI,CAACvB,GAAG,CAAC,YAAY,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACgB,OAAO,CAAC;IACvD,IAAI,CAACxB,GAAG,CAAC,kBAAkB,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACiB,aAAa,CAAC;IACnE,IAAI,CAACzB,GAAG,CAAC,gBAAgB,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACkB,WAAW,CAAC;IAC/D,IAAI,CAAC1B,GAAG,CAAC,iBAAiB,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACmB,OAAO,CAAC;IAC5D,IAAI,CAAC3B,GAAG,CAAC,kBAAkB,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACoB,QAAQ,CAAC;IAE9DrB,SAAS,CAACsB,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,mBAAmB;MACxB,KAAK,gBAAgB;QACnB,OAAO,IAAI;MACb;QACE,IAAI,CAACD,MAAM,GAAG,IAAI,CAAChC,GAAG,CAAC+B,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;UAC3B,OAAO,IAAI;QACb;QACA,MAAM,IAAIG,KAAK,CAAE,qCAAoCC,IAAI,CAACC,SAAS,CAACL,IAAI,CAAE,EAAC,CAAC;IAChF;EACF;EAEAM,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACN,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACK,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACN,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACO,UAAU,CAACN,IAAI,CAAC,EAAE;QACjC,IAAI,CAACD,MAAM,GAAGQ,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQP,IAAI;MACV,KAAK,mBAAmB;MACxB,KAAK,gBAAgB;QACnB,IAAI,CAACzB,KAAK,GAAG;UACXK,OAAO,EAAE,IAAI,CAACb,GAAG,CAAC,YAAY,CAAC,CAACQ,KAAK;UACrCM,KAAK,EAAE,IAAI,CAACd,GAAG,CAAC,UAAU,CAAC,CAACQ,KAAK;UACjCO,OAAO,EAAE,IAAI,CAACf,GAAG,CAAC,YAAY,CAAC,CAACQ,KAAK;UACrCQ,WAAW,EAAE,IAAI,CAAChB,GAAG,CAAC,gBAAgB,CAAC,CAACQ,KAAK;UAC7CS,UAAU,EAAE,IAAI,CAACjB,GAAG,CAAC,eAAe,CAAC,CAACQ,KAAK;UAC3CU,QAAQ,EAAE,IAAI,CAAClB,GAAG,CAAC,aAAa,CAAC,CAACQ,KAAK;UACvCW,QAAQ,EAAE,IAAI,CAACnB,GAAG,CAAC,aAAa,CAAC,CAACQ,KAAK;UACvCY,QAAQ,EAAE,IAAI,CAACpB,GAAG,CAAC,aAAa,CAAC,CAACQ,KAAK;UACvCa,cAAc,EAAE,IAAI,CAACrB,GAAG,CAAC,mBAAmB,CAAC,CAACQ,KAAK;UACnDc,WAAW,EAAE,IAAI,CAACtB,GAAG,CAAC,gBAAgB,CAAC,CAACQ,KAAK;UAC7Ce,QAAQ,EAAE,IAAI,CAACvB,GAAG,CAAC,aAAa,CAAC,CAACQ,KAAK;UACvCiB,aAAa,EAAE,IAAI,CAACzB,GAAG,CAAC,kBAAkB,CAAC,CAACQ,KAAK;UACjDkB,WAAW,EAAE,IAAI,CAAC1B,GAAG,CAAC,gBAAgB,CAAC,CAACQ,KAAK;UAC7CmB,OAAO,EAAE,IAAI,CAAC3B,GAAG,CAAC,iBAAiB,CAAC,CAACQ,KAAK;UAC1CoB,QAAQ,EAAE,IAAI,CAAC5B,GAAG,CAAC,kBAAkB,CAAC,CAACQ;QACzC,CAAC;QACD,OAAO,KAAK;MACd;QACE,MAAM,IAAI0B,KAAK,CAAE,sCAAqCD,IAAK,EAAC,CAAC;IACjE;EACF;AACF;AAEAnC,SAAS,CAACK,UAAU,GAAG,UAASsC,EAAE,EAAE;EAClC,OAAOA,EAAE,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;AACjD,CAAC;AACD7C,SAAS,CAACO,SAAS,GAAG;EAAC,UAAU,EAAE;AAAgB,CAAC;AAEpDP,SAAS,CAACc,wBAAwB,GAAG;EACnC,UAAU,EAAE,yEAAyE;EACrF,UAAU,EAAE,kCAAkC;EAC9C,eAAe,EAAE,2BAA2B;EAC5C,gBAAgB,EAAE,8BAA8B;EAChD,WAAW,EAAE;AACf,CAAC;AAEDgC,MAAM,CAACC,OAAO,GAAG/C,SAAS"}