{"version": 3, "file": "auto-filter-xform.js", "names": ["co<PERSON><PERSON><PERSON>", "require", "BaseXform", "AutoFilterXform", "tag", "render", "xmlStream", "model", "leafNode", "ref", "get<PERSON><PERSON><PERSON>", "addr", "row", "column", "address", "firstAddress", "from", "second<PERSON><PERSON><PERSON>", "to", "parseOpen", "node", "name", "attributes", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/auto-filter-xform.js"], "sourcesContent": ["const colCache = require('../../../utils/col-cache');\nconst BaseXform = require('../base-xform');\n\nclass AutoFilterXform extends BaseXform {\n  get tag() {\n    return 'autoFilter';\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      if (typeof model === 'string') {\n        // assume range\n        xmlStream.leafNode('autoFilter', {ref: model});\n      } else {\n        const getAddress = function(addr) {\n          if (typeof addr === 'string') {\n            return addr;\n          }\n          return colCache.getAddress(addr.row, addr.column).address;\n        };\n\n        const firstAddress = getAddress(model.from);\n        const secondAddress = getAddress(model.to);\n        if (firstAddress && secondAddress) {\n          xmlStream.leafNode('autoFilter', {ref: `${firstAddress}:${secondAddress}`});\n        }\n      }\n    }\n  }\n\n  parseOpen(node) {\n    if (node.name === 'autoFilter') {\n      this.model = node.attributes.ref;\n    }\n  }\n}\n\nmodule.exports = AutoFilterXform;\n"], "mappings": ";;AAAA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAME,eAAe,SAASD,SAAS,CAAC;EACtC,IAAIE,GAAGA,CAAA,EAAG;IACR,OAAO,YAAY;EACrB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACT,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACAD,SAAS,CAACE,QAAQ,CAAC,YAAY,EAAE;UAACC,GAAG,EAAEF;QAAK,CAAC,CAAC;MAChD,CAAC,MAAM;QACL,MAAMG,UAAU,GAAG,SAAAA,CAASC,IAAI,EAAE;UAChC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;YAC5B,OAAOA,IAAI;UACb;UACA,OAAOX,QAAQ,CAACU,UAAU,CAACC,IAAI,CAACC,GAAG,EAAED,IAAI,CAACE,MAAM,CAAC,CAACC,OAAO;QAC3D,CAAC;QAED,MAAMC,YAAY,GAAGL,UAAU,CAACH,KAAK,CAACS,IAAI,CAAC;QAC3C,MAAMC,aAAa,GAAGP,UAAU,CAACH,KAAK,CAACW,EAAE,CAAC;QAC1C,IAAIH,YAAY,IAAIE,aAAa,EAAE;UACjCX,SAAS,CAACE,QAAQ,CAAC,YAAY,EAAE;YAACC,GAAG,EAAG,GAAEM,YAAa,IAAGE,aAAc;UAAC,CAAC,CAAC;QAC7E;MACF;IACF;EACF;EAEAE,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,YAAY,EAAE;MAC9B,IAAI,CAACd,KAAK,GAAGa,IAAI,CAACE,UAAU,CAACb,GAAG;IAClC;EACF;AACF;AAEAc,MAAM,CAACC,OAAO,GAAGrB,eAAe"}