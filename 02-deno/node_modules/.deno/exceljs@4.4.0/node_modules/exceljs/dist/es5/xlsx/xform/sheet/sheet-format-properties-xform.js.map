{"version": 3, "file": "sheet-format-properties-xform.js", "names": ["_", "require", "BaseXform", "SheetFormatPropertiesXform", "tag", "render", "xmlStream", "model", "attributes", "defaultRowHeight", "outlineLevelRow", "outlineLevelCol", "dyDescent", "defaultColWidth", "customHeight", "some", "value", "undefined", "leafNode", "parseOpen", "node", "name", "parseFloat", "parseInt", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/sheet-format-properties-xform.js"], "sourcesContent": ["const _ = require('../../../utils/under-dash');\nconst BaseXform = require('../base-xform');\n\nclass SheetFormatPropertiesXform extends BaseXform {\n  get tag() {\n    return 'sheetFormatPr';\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      const attributes = {\n        defaultRowHeight: model.defaultRowHeight,\n        outlineLevelRow: model.outlineLevelRow,\n        outlineLevelCol: model.outlineLevelCol,\n        'x14ac:dyDescent': model.dyDescent,\n      };\n      if (model.defaultColWidth) {\n        attributes.defaultColWidth = model.defaultColWidth;\n      }\n\n      // default value for 'defaultRowHeight' is 15, this should not be 'custom'\n      if (!model.defaultRowHeight || model.defaultRowHeight !== 15) {\n        attributes.customHeight = '1';\n      }\n\n      if (_.some(attributes, value => value !== undefined)) {\n        xmlStream.leafNode('sheetFormatPr', attributes);\n      }\n    }\n  }\n\n  parseOpen(node) {\n    if (node.name === 'sheetFormatPr') {\n      this.model = {\n        defaultRowHeight: parseFloat(node.attributes.defaultRowHeight || '0'),\n        dyDescent: parseFloat(node.attributes['x14ac:dyDescent'] || '0'),\n        outlineLevelRow: parseInt(node.attributes.outlineLevelRow || '0', 10),\n        outlineLevelCol: parseInt(node.attributes.outlineLevelCol || '0', 10),\n      };\n      if (node.attributes.defaultColWidth) {\n        this.model.defaultColWidth = parseFloat(node.attributes.defaultColWidth);\n      }\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = SheetFormatPropertiesXform;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAC9C,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAME,0BAA0B,SAASD,SAAS,CAAC;EACjD,IAAIE,GAAGA,CAAA,EAAG;IACR,OAAO,eAAe;EACxB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACT,MAAMC,UAAU,GAAG;QACjBC,gBAAgB,EAAEF,KAAK,CAACE,gBAAgB;QACxCC,eAAe,EAAEH,KAAK,CAACG,eAAe;QACtCC,eAAe,EAAEJ,KAAK,CAACI,eAAe;QACtC,iBAAiB,EAAEJ,KAAK,CAACK;MAC3B,CAAC;MACD,IAAIL,KAAK,CAACM,eAAe,EAAE;QACzBL,UAAU,CAACK,eAAe,GAAGN,KAAK,CAACM,eAAe;MACpD;;MAEA;MACA,IAAI,CAACN,KAAK,CAACE,gBAAgB,IAAIF,KAAK,CAACE,gBAAgB,KAAK,EAAE,EAAE;QAC5DD,UAAU,CAACM,YAAY,GAAG,GAAG;MAC/B;MAEA,IAAId,CAAC,CAACe,IAAI,CAACP,UAAU,EAAEQ,KAAK,IAAIA,KAAK,KAAKC,SAAS,CAAC,EAAE;QACpDX,SAAS,CAACY,QAAQ,CAAC,eAAe,EAAEV,UAAU,CAAC;MACjD;IACF;EACF;EAEAW,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,eAAe,EAAE;MACjC,IAAI,CAACd,KAAK,GAAG;QACXE,gBAAgB,EAAEa,UAAU,CAACF,IAAI,CAACZ,UAAU,CAACC,gBAAgB,IAAI,GAAG,CAAC;QACrEG,SAAS,EAAEU,UAAU,CAACF,IAAI,CAACZ,UAAU,CAAC,iBAAiB,CAAC,IAAI,GAAG,CAAC;QAChEE,eAAe,EAAEa,QAAQ,CAACH,IAAI,CAACZ,UAAU,CAACE,eAAe,IAAI,GAAG,EAAE,EAAE,CAAC;QACrEC,eAAe,EAAEY,QAAQ,CAACH,IAAI,CAACZ,UAAU,CAACG,eAAe,IAAI,GAAG,EAAE,EAAE;MACtE,CAAC;MACD,IAAIS,IAAI,CAACZ,UAAU,CAACK,eAAe,EAAE;QACnC,IAAI,CAACN,KAAK,CAACM,eAAe,GAAGS,UAAU,CAACF,IAAI,CAACZ,UAAU,CAACK,eAAe,CAAC;MAC1E;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAW,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGxB,0BAA0B"}