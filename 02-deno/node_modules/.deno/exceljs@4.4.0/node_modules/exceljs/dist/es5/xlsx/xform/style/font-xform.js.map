{"version": 3, "file": "font-xform.js", "names": ["ColorXform", "require", "BooleanXform", "IntegerXform", "StringXform", "UnderlineXform", "_", "BaseXform", "FontXform", "constructor", "options", "OPTIONS", "map", "b", "prop", "xform", "tag", "attr", "i", "u", "charset", "color", "condense", "extend", "family", "outline", "vertAlign", "scheme", "shadow", "strike", "sz", "fontNameTag", "tagName", "render", "xmlStream", "model", "openNode", "each", "defn", "closeNode", "parseOpen", "node", "parser", "name", "parseText", "text", "parseClose", "item", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/font-xform.js"], "sourcesContent": ["'use strict';\n\nconst ColorXform = require('./color-xform');\nconst BooleanXform = require('../simple/boolean-xform');\nconst IntegerXform = require('../simple/integer-xform');\nconst StringXform = require('../simple/string-xform');\nconst UnderlineXform = require('./underline-xform');\n\nconst _ = require('../../../utils/under-dash');\nconst BaseXform = require('../base-xform');\n\n// Font encapsulates translation from font model to xlsx\nclass FontXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    this.options = options || FontXform.OPTIONS;\n\n    this.map = {\n      b: {prop: 'bold', xform: new BooleanXform({tag: 'b', attr: 'val'})},\n      i: {prop: 'italic', xform: new BooleanXform({tag: 'i', attr: 'val'})},\n      u: {prop: 'underline', xform: new UnderlineXform()},\n      charset: {prop: 'charset', xform: new IntegerXform({tag: 'charset', attr: 'val'})},\n      color: {prop: 'color', xform: new ColorXform()},\n      condense: {prop: 'condense', xform: new BooleanXform({tag: 'condense', attr: 'val'})},\n      extend: {prop: 'extend', xform: new BooleanXform({tag: 'extend', attr: 'val'})},\n      family: {prop: 'family', xform: new IntegerXform({tag: 'family', attr: 'val'})},\n      outline: {prop: 'outline', xform: new BooleanXform({tag: 'outline', attr: 'val'})},\n      vertAlign: {prop: 'vertAlign', xform: new StringXform({tag: 'vertAlign', attr: 'val'})},\n      scheme: {prop: 'scheme', xform: new StringXform({tag: 'scheme', attr: 'val'})},\n      shadow: {prop: 'shadow', xform: new BooleanXform({tag: 'shadow', attr: 'val'})},\n      strike: {prop: 'strike', xform: new BooleanXform({tag: 'strike', attr: 'val'})},\n      sz: {prop: 'size', xform: new IntegerXform({tag: 'sz', attr: 'val'})},\n    };\n    this.map[this.options.fontNameTag] = {\n      prop: 'name',\n      xform: new StringXform({tag: this.options.fontNameTag, attr: 'val'}),\n    };\n  }\n\n  get tag() {\n    return this.options.tagName;\n  }\n\n  render(xmlStream, model) {\n    const {map} = this;\n\n    xmlStream.openNode(this.options.tagName);\n    _.each(this.map, (defn, tag) => {\n      map[tag].xform.render(xmlStream, model[defn.prop]);\n    });\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    if (this.map[node.name]) {\n      this.parser = this.map[node.name].xform;\n      return this.parser.parseOpen(node);\n    }\n    switch (node.name) {\n      case this.options.tagName:\n        this.model = {};\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser && !this.parser.parseClose(name)) {\n      const item = this.map[name];\n      if (this.parser.model) {\n        this.model[item.prop] = this.parser.model;\n      }\n      this.parser = undefined;\n      return true;\n    }\n    switch (name) {\n      case this.options.tagName:\n        return false;\n      default:\n        return true;\n    }\n  }\n}\n\nFontXform.OPTIONS = {\n  tagName: 'font',\n  fontNameTag: 'name',\n};\n\nmodule.exports = FontXform;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,MAAMC,YAAY,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AACvD,MAAME,YAAY,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACvD,MAAMG,WAAW,GAAGH,OAAO,CAAC,wBAAwB,CAAC;AACrD,MAAMI,cAAc,GAAGJ,OAAO,CAAC,mBAAmB,CAAC;AAEnD,MAAMK,CAAC,GAAGL,OAAO,CAAC,2BAA2B,CAAC;AAC9C,MAAMM,SAAS,GAAGN,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA,MAAMO,SAAS,SAASD,SAAS,CAAC;EAChCE,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAIF,SAAS,CAACG,OAAO;IAE3C,IAAI,CAACC,GAAG,GAAG;MACTC,CAAC,EAAE;QAACC,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAIb,YAAY,CAAC;UAACc,GAAG,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MACnEC,CAAC,EAAE;QAACJ,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,IAAIb,YAAY,CAAC;UAACc,GAAG,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MACrEE,CAAC,EAAE;QAACL,IAAI,EAAE,WAAW;QAAEC,KAAK,EAAE,IAAIV,cAAc,CAAC;MAAC,CAAC;MACnDe,OAAO,EAAE;QAACN,IAAI,EAAE,SAAS;QAAEC,KAAK,EAAE,IAAIZ,YAAY,CAAC;UAACa,GAAG,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MAClFI,KAAK,EAAE;QAACP,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,IAAIf,UAAU,CAAC;MAAC,CAAC;MAC/CsB,QAAQ,EAAE;QAACR,IAAI,EAAE,UAAU;QAAEC,KAAK,EAAE,IAAIb,YAAY,CAAC;UAACc,GAAG,EAAE,UAAU;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MACrFM,MAAM,EAAE;QAACT,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,IAAIb,YAAY,CAAC;UAACc,GAAG,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MAC/EO,MAAM,EAAE;QAACV,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,IAAIZ,YAAY,CAAC;UAACa,GAAG,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MAC/EQ,OAAO,EAAE;QAACX,IAAI,EAAE,SAAS;QAAEC,KAAK,EAAE,IAAIb,YAAY,CAAC;UAACc,GAAG,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MAClFS,SAAS,EAAE;QAACZ,IAAI,EAAE,WAAW;QAAEC,KAAK,EAAE,IAAIX,WAAW,CAAC;UAACY,GAAG,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MACvFU,MAAM,EAAE;QAACb,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,IAAIX,WAAW,CAAC;UAACY,GAAG,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MAC9EW,MAAM,EAAE;QAACd,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,IAAIb,YAAY,CAAC;UAACc,GAAG,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MAC/EY,MAAM,EAAE;QAACf,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,IAAIb,YAAY,CAAC;UAACc,GAAG,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC,CAAC;MAC/Ea,EAAE,EAAE;QAAChB,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAIZ,YAAY,CAAC;UAACa,GAAG,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAK,CAAC;MAAC;IACtE,CAAC;IACD,IAAI,CAACL,GAAG,CAAC,IAAI,CAACF,OAAO,CAACqB,WAAW,CAAC,GAAG;MACnCjB,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAIX,WAAW,CAAC;QAACY,GAAG,EAAE,IAAI,CAACN,OAAO,CAACqB,WAAW;QAAEd,IAAI,EAAE;MAAK,CAAC;IACrE,CAAC;EACH;EAEA,IAAID,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACN,OAAO,CAACsB,OAAO;EAC7B;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,MAAM;MAACvB;IAAG,CAAC,GAAG,IAAI;IAElBsB,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAC1B,OAAO,CAACsB,OAAO,CAAC;IACxC1B,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAACzB,GAAG,EAAE,CAAC0B,IAAI,EAAEtB,GAAG,KAAK;MAC9BJ,GAAG,CAACI,GAAG,CAAC,CAACD,KAAK,CAACkB,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACG,IAAI,CAACxB,IAAI,CAAC,CAAC;IACpD,CAAC,CAAC;IACFoB,SAAS,CAACK,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,IAAI,IAAI,CAAC7B,GAAG,CAAC6B,IAAI,CAACE,IAAI,CAAC,EAAE;MACvB,IAAI,CAACD,MAAM,GAAG,IAAI,CAAC9B,GAAG,CAAC6B,IAAI,CAACE,IAAI,CAAC,CAAC5B,KAAK;MACvC,OAAO,IAAI,CAAC2B,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;IACpC;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACjC,OAAO,CAACsB,OAAO;QACvB,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;QACf,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAS,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACE,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACH,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAACI,UAAU,CAACH,IAAI,CAAC,EAAE;MAChD,MAAMI,IAAI,GAAG,IAAI,CAACnC,GAAG,CAAC+B,IAAI,CAAC;MAC3B,IAAI,IAAI,CAACD,MAAM,CAACP,KAAK,EAAE;QACrB,IAAI,CAACA,KAAK,CAACY,IAAI,CAACjC,IAAI,CAAC,GAAG,IAAI,CAAC4B,MAAM,CAACP,KAAK;MAC3C;MACA,IAAI,CAACO,MAAM,GAAGM,SAAS;MACvB,OAAO,IAAI;IACb;IACA,QAAQL,IAAI;MACV,KAAK,IAAI,CAACjC,OAAO,CAACsB,OAAO;QACvB,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEAxB,SAAS,CAACG,OAAO,GAAG;EAClBqB,OAAO,EAAE,MAAM;EACfD,WAAW,EAAE;AACf,CAAC;AAEDkB,MAAM,CAACC,OAAO,GAAG1C,SAAS"}