{"version": 3, "file": "page-setup-properties-xform.js", "names": ["BaseXform", "require", "PageSetupPropertiesXform", "tag", "render", "xmlStream", "model", "fitToPage", "leafNode", "undefined", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/page-setup-properties-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass PageSetupPropertiesXform extends BaseXform {\n  get tag() {\n    return 'pageSetUpPr';\n  }\n\n  render(xmlStream, model) {\n    if (model && model.fitToPage) {\n      xmlStream.leafNode(this.tag, {\n        fitToPage: model.fitToPage ? '1' : undefined,\n      });\n      return true;\n    }\n    return false;\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      this.model = {\n        fitToPage: node.attributes.fitToPage === '1',\n      };\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = PageSetupPropertiesXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,wBAAwB,SAASF,SAAS,CAAC;EAC/C,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,aAAa;EACtB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,IAAIA,KAAK,CAACC,SAAS,EAAE;MAC5BF,SAAS,CAACG,QAAQ,CAAC,IAAI,CAACL,GAAG,EAAE;QAC3BI,SAAS,EAAED,KAAK,CAACC,SAAS,GAAG,GAAG,GAAGE;MACrC,CAAC,CAAC;MACF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,IAAI,CAACT,GAAG,EAAE;MAC1B,IAAI,CAACG,KAAK,GAAG;QACXC,SAAS,EAAEI,IAAI,CAACE,UAAU,CAACN,SAAS,KAAK;MAC3C,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAO,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGf,wBAAwB"}