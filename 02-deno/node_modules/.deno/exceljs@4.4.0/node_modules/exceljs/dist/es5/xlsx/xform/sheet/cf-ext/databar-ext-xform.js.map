{"version": 3, "file": "databar-ext-xform.js", "names": ["BaseXform", "require", "CompositeXform", "ColorXform", "CfvoExtXform", "DatabarExtXform", "constructor", "map", "cfvoXform", "borderColorXform", "negativeBorderColorXform", "negativeFillColorXform", "axisColorXform", "isExt", "rule", "gradient", "tag", "render", "xmlStream", "model", "openNode", "<PERSON><PERSON><PERSON><PERSON>", "toIntAttribute", "max<PERSON><PERSON><PERSON>", "border", "toBoolAttribute", "negativeBarColorSameAsPositive", "negativeBarBorderColorSameAsPositive", "axisPosition", "toAttribute", "direction", "cfvo", "for<PERSON>ach", "borderColor", "negativeBorderColor", "negativeFillColor", "axisColor", "closeNode", "createNewModel", "_ref", "attributes", "toIntValue", "toBoolValue", "toStringValue", "onParserClose", "name", "parser", "prop", "split", "push", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf-ext/databar-ext-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\nconst CompositeXform = require('../../composite-xform');\n\nconst ColorXform = require('../../style/color-xform');\nconst CfvoExtXform = require('./cfvo-ext-xform');\n\nclass DatabarExtXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'x14:cfvo': (this.cfvoXform = new CfvoExtXform()),\n      'x14:borderColor': (this.borderColorXform = new ColorXform('x14:borderColor')),\n      'x14:negativeBorderColor': (this.negativeBorderColorXform = new ColorXform(\n        'x14:negativeBorderColor'\n      )),\n      'x14:negativeFillColor': (this.negativeFillColorXform = new ColorXform(\n        'x14:negativeFillColor'\n      )),\n      'x14:axisColor': (this.axisColorXform = new ColorXform('x14:axisColor')),\n    };\n  }\n\n  static isExt(rule) {\n    // not all databars need ext\n    // TODO: refine this\n    return !rule.gradient;\n  }\n\n  get tag() {\n    return 'x14:dataBar';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      minLength: BaseXform.toIntAttribute(model.minLength, 0, true),\n      maxLength: BaseXform.toIntAttribute(model.maxLength, 100, true),\n      border: BaseXform.toBoolAttribute(model.border, false),\n      gradient: BaseXform.toBoolAttribute(model.gradient, true),\n      negativeBarColorSameAsPositive: BaseXform.toBoolAttribute(\n        model.negativeBarColorSameAsPositive,\n        true\n      ),\n      negativeBarBorderColorSameAsPositive: BaseXform.toBoolAttribute(\n        model.negativeBarBorderColorSameAsPositive,\n        true\n      ),\n      axisPosition: BaseXform.toAttribute(model.axisPosition, 'auto'),\n      direction: BaseXform.toAttribute(model.direction, 'leftToRight'),\n    });\n\n    model.cfvo.forEach(cfvo => {\n      this.cfvoXform.render(xmlStream, cfvo);\n    });\n\n    this.borderColorXform.render(xmlStream, model.borderColor);\n    this.negativeBorderColorXform.render(xmlStream, model.negativeBorderColor);\n    this.negativeFillColorXform.render(xmlStream, model.negativeFillColor);\n    this.axisColorXform.render(xmlStream, model.axisColor);\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel({attributes}) {\n    return {\n      cfvo: [],\n      minLength: BaseXform.toIntValue(attributes.minLength, 0),\n      maxLength: BaseXform.toIntValue(attributes.maxLength, 100),\n      border: BaseXform.toBoolValue(attributes.border, false),\n      gradient: BaseXform.toBoolValue(attributes.gradient, true),\n      negativeBarColorSameAsPositive: BaseXform.toBoolValue(\n        attributes.negativeBarColorSameAsPositive,\n        true\n      ),\n      negativeBarBorderColorSameAsPositive: BaseXform.toBoolValue(\n        attributes.negativeBarBorderColorSameAsPositive,\n        true\n      ),\n      axisPosition: BaseXform.toStringValue(attributes.axisPosition, 'auto'),\n      direction: BaseXform.toStringValue(attributes.direction, 'leftToRight'),\n    };\n  }\n\n  onParserClose(name, parser) {\n    const [, prop] = name.split(':');\n    switch (prop) {\n      case 'cfvo':\n        this.model.cfvo.push(parser.model);\n        break;\n\n      default:\n        this.model[prop] = parser.model;\n        break;\n    }\n  }\n}\n\nmodule.exports = DatabarExtXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC7C,MAAMC,cAAc,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAME,UAAU,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACrD,MAAMG,YAAY,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AAEhD,MAAMI,eAAe,SAASH,cAAc,CAAC;EAC3CI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,UAAU,EAAG,IAAI,CAACC,SAAS,GAAG,IAAIJ,YAAY,CAAC,CAAE;MACjD,iBAAiB,EAAG,IAAI,CAACK,gBAAgB,GAAG,IAAIN,UAAU,CAAC,iBAAiB,CAAE;MAC9E,yBAAyB,EAAG,IAAI,CAACO,wBAAwB,GAAG,IAAIP,UAAU,CACxE,yBACF,CAAE;MACF,uBAAuB,EAAG,IAAI,CAACQ,sBAAsB,GAAG,IAAIR,UAAU,CACpE,uBACF,CAAE;MACF,eAAe,EAAG,IAAI,CAACS,cAAc,GAAG,IAAIT,UAAU,CAAC,eAAe;IACxE,CAAC;EACH;EAEA,OAAOU,KAAKA,CAACC,IAAI,EAAE;IACjB;IACA;IACA,OAAO,CAACA,IAAI,CAACC,QAAQ;EACvB;EAEA,IAAIC,GAAGA,CAAA,EAAG;IACR,OAAO,aAAa;EACtB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,SAAS,EAAErB,SAAS,CAACsB,cAAc,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC;MAC7DE,SAAS,EAAEvB,SAAS,CAACsB,cAAc,CAACH,KAAK,CAACI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC;MAC/DC,MAAM,EAAExB,SAAS,CAACyB,eAAe,CAACN,KAAK,CAACK,MAAM,EAAE,KAAK,CAAC;MACtDT,QAAQ,EAAEf,SAAS,CAACyB,eAAe,CAACN,KAAK,CAACJ,QAAQ,EAAE,IAAI,CAAC;MACzDW,8BAA8B,EAAE1B,SAAS,CAACyB,eAAe,CACvDN,KAAK,CAACO,8BAA8B,EACpC,IACF,CAAC;MACDC,oCAAoC,EAAE3B,SAAS,CAACyB,eAAe,CAC7DN,KAAK,CAACQ,oCAAoC,EAC1C,IACF,CAAC;MACDC,YAAY,EAAE5B,SAAS,CAAC6B,WAAW,CAACV,KAAK,CAACS,YAAY,EAAE,MAAM,CAAC;MAC/DE,SAAS,EAAE9B,SAAS,CAAC6B,WAAW,CAACV,KAAK,CAACW,SAAS,EAAE,aAAa;IACjE,CAAC,CAAC;IAEFX,KAAK,CAACY,IAAI,CAACC,OAAO,CAACD,IAAI,IAAI;MACzB,IAAI,CAACvB,SAAS,CAACS,MAAM,CAACC,SAAS,EAAEa,IAAI,CAAC;IACxC,CAAC,CAAC;IAEF,IAAI,CAACtB,gBAAgB,CAACQ,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACc,WAAW,CAAC;IAC1D,IAAI,CAACvB,wBAAwB,CAACO,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACe,mBAAmB,CAAC;IAC1E,IAAI,CAACvB,sBAAsB,CAACM,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACgB,iBAAiB,CAAC;IACtE,IAAI,CAACvB,cAAc,CAACK,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACiB,SAAS,CAAC;IAEtDlB,SAAS,CAACmB,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAAAC,IAAA,EAAe;IAAA,IAAd;MAACC;IAAU,CAAC,GAAAD,IAAA;IACzB,OAAO;MACLR,IAAI,EAAE,EAAE;MACRV,SAAS,EAAErB,SAAS,CAACyC,UAAU,CAACD,UAAU,CAACnB,SAAS,EAAE,CAAC,CAAC;MACxDE,SAAS,EAAEvB,SAAS,CAACyC,UAAU,CAACD,UAAU,CAACjB,SAAS,EAAE,GAAG,CAAC;MAC1DC,MAAM,EAAExB,SAAS,CAAC0C,WAAW,CAACF,UAAU,CAAChB,MAAM,EAAE,KAAK,CAAC;MACvDT,QAAQ,EAAEf,SAAS,CAAC0C,WAAW,CAACF,UAAU,CAACzB,QAAQ,EAAE,IAAI,CAAC;MAC1DW,8BAA8B,EAAE1B,SAAS,CAAC0C,WAAW,CACnDF,UAAU,CAACd,8BAA8B,EACzC,IACF,CAAC;MACDC,oCAAoC,EAAE3B,SAAS,CAAC0C,WAAW,CACzDF,UAAU,CAACb,oCAAoC,EAC/C,IACF,CAAC;MACDC,YAAY,EAAE5B,SAAS,CAAC2C,aAAa,CAACH,UAAU,CAACZ,YAAY,EAAE,MAAM,CAAC;MACtEE,SAAS,EAAE9B,SAAS,CAAC2C,aAAa,CAACH,UAAU,CAACV,SAAS,EAAE,aAAa;IACxE,CAAC;EACH;EAEAc,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B,MAAM,GAAGC,IAAI,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;IAChC,QAAQD,IAAI;MACV,KAAK,MAAM;QACT,IAAI,CAAC5B,KAAK,CAACY,IAAI,CAACkB,IAAI,CAACH,MAAM,CAAC3B,KAAK,CAAC;QAClC;MAEF;QACE,IAAI,CAACA,KAAK,CAAC4B,IAAI,CAAC,GAAGD,MAAM,CAAC3B,KAAK;QAC/B;IACJ;EACF;AACF;AAEA+B,MAAM,CAACC,OAAO,GAAG9C,eAAe"}