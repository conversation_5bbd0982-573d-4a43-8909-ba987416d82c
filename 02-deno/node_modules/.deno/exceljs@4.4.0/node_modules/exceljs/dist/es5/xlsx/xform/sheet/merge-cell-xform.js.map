{"version": 3, "file": "merge-cell-xform.js", "names": ["BaseXform", "require", "MergeCellXform", "tag", "render", "xmlStream", "model", "leafNode", "ref", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/merge-cell-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass MergeCellXform extends BaseXform {\n  get tag() {\n    return 'mergeCell';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode('mergeCell', {ref: model});\n  }\n\n  parseOpen(node) {\n    if (node.name === 'mergeCell') {\n      this.model = node.attributes.ref;\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = MergeCellXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,cAAc,SAASF,SAAS,CAAC;EACrC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,WAAW;EACpB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,WAAW,EAAE;MAACC,GAAG,EAAEF;IAAK,CAAC,CAAC;EAC/C;EAEAG,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC7B,IAAI,CAACL,KAAK,GAAGI,IAAI,CAACE,UAAU,CAACJ,GAAG;MAChC,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAK,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGd,cAAc"}