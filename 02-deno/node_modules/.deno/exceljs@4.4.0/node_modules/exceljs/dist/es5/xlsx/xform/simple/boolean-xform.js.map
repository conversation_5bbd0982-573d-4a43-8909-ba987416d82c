{"version": 3, "file": "boolean-xform.js", "names": ["BaseXform", "require", "BooleanXform", "constructor", "options", "tag", "attr", "render", "xmlStream", "model", "openNode", "closeNode", "parseOpen", "node", "name", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/simple/boolean-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass BooleanXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    this.tag = options.tag;\n    this.attr = options.attr;\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      xmlStream.openNode(this.tag);\n      xmlStream.closeNode();\n    }\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      this.model = true;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = BooleanXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,YAAY,SAASF,SAAS,CAAC;EACnCG,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAGD,OAAO,CAACC,GAAG;IACtB,IAAI,CAACC,IAAI,GAAGF,OAAO,CAACE,IAAI;EAC1B;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACTD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACL,GAAG,CAAC;MAC5BG,SAAS,CAACG,SAAS,CAAC,CAAC;IACvB;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,IAAI,CAACT,GAAG,EAAE;MAC1B,IAAI,CAACI,KAAK,GAAG,IAAI;IACnB;EACF;EAEAM,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGhB,YAAY"}