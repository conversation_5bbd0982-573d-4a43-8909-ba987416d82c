{"version": 3, "file": "exceljs.nodejs.js", "names": ["ExcelJS", "Workbook", "require", "ModelContainer", "stream", "xlsx", "WorkbookWriter", "WorkbookReader", "Object", "assign", "module", "exports"], "sources": ["../../lib/exceljs.nodejs.js"], "sourcesContent": ["const ExcelJS = {\n  Workbook: require('./doc/workbook'),\n  ModelContainer: require('./doc/modelcontainer'),\n  stream: {\n    xlsx: {\n      WorkbookWriter: require('./stream/xlsx/workbook-writer'),\n      WorkbookReader: require('./stream/xlsx/workbook-reader'),\n    },\n  },\n};\n\nObject.assign(ExcelJS, require('./doc/enums'));\n\nmodule.exports = ExcelJS;\n"], "mappings": ";;AAAA,MAAMA,OAAO,GAAG;EACdC,QAAQ,EAAEC,OAAO,CAAC,gBAAgB,CAAC;EACnCC,cAAc,EAAED,OAAO,CAAC,sBAAsB,CAAC;EAC/CE,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,cAAc,EAAEJ,OAAO,CAAC,+BAA+B,CAAC;MACxDK,cAAc,EAAEL,OAAO,CAAC,+BAA+B;IACzD;EACF;AACF,CAAC;AAEDM,MAAM,CAACC,MAAM,CAACT,OAAO,EAAEE,OAAO,CAAC,aAAa,CAAC,CAAC;AAE9CQ,MAAM,CAACC,OAAO,GAAGX,OAAO"}