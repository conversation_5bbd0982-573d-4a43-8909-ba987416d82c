{"version": 3, "file": "string-builder.js", "names": ["StringBuilder", "constructor", "reset", "length", "_buf", "toString", "join", "position", "pop", "addText", "text", "push", "addStringBuf", "inBuf", "module", "exports"], "sources": ["../../../lib/utils/string-builder.js"], "sourcesContent": ["// StringBuilder - a way to keep string memory operations to a minimum\n// while building the strings for the xml files\nclass StringBuilder {\n  constructor() {\n    this.reset();\n  }\n\n  get length() {\n    return this._buf.length;\n  }\n\n  toString() {\n    return this._buf.join('');\n  }\n\n  reset(position) {\n    if (position) {\n      while (this._buf.length > position) {\n        this._buf.pop();\n      }\n    } else {\n      this._buf = [];\n    }\n  }\n\n  addText(text) {\n    this._buf.push(text);\n  }\n\n  addStringBuf(inBuf) {\n    this._buf.push(inBuf.toString());\n  }\n}\n\nmodule.exports = StringBuilder;\n"], "mappings": ";;AAAA;AACA;AACA,MAAMA,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAIC,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,IAAI,CAACD,MAAM;EACzB;EAEAE,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE,CAAC;EAC3B;EAEAJ,KAAKA,CAACK,QAAQ,EAAE;IACd,IAAIA,QAAQ,EAAE;MACZ,OAAO,IAAI,CAACH,IAAI,CAACD,MAAM,GAAGI,QAAQ,EAAE;QAClC,IAAI,CAACH,IAAI,CAACI,GAAG,CAAC,CAAC;MACjB;IACF,CAAC,MAAM;MACL,IAAI,CAACJ,IAAI,GAAG,EAAE;IAChB;EACF;EAEAK,OAAOA,CAACC,IAAI,EAAE;IACZ,IAAI,CAACN,IAAI,CAACO,IAAI,CAACD,IAAI,CAAC;EACtB;EAEAE,YAAYA,CAACC,KAAK,EAAE;IAClB,IAAI,CAACT,IAAI,CAACO,IAAI,CAACE,KAAK,CAACR,QAAQ,CAAC,CAAC,CAAC;EAClC;AACF;AAEAS,MAAM,CAACC,OAAO,GAAGf,aAAa"}