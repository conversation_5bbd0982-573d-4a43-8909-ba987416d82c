{"version": 3, "file": "drawing-xform.js", "names": ["co<PERSON><PERSON><PERSON>", "require", "XmlStream", "BaseXform", "TwoCellAnchorXform", "OneCellAnchorXform", "getAnchorType", "model", "range", "decode", "br", "DrawingXform", "constructor", "map", "prepare", "anchors", "for<PERSON>ach", "item", "index", "anchorType", "anchor", "tag", "render", "xmlStream", "openXml", "StdDocAttributes", "openNode", "DRAWING_ATTRIBUTES", "closeNode", "parseOpen", "node", "parser", "name", "reset", "parseText", "text", "parseClose", "push", "undefined", "reconcile", "options", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/drawing-xform.js"], "sourcesContent": ["const colCache = require('../../../utils/col-cache');\nconst XmlStream = require('../../../utils/xml-stream');\n\nconst BaseXform = require('../base-xform');\nconst TwoCellAnchorXform = require('./two-cell-anchor-xform');\nconst OneCellAnchorXform = require('./one-cell-anchor-xform');\n\nfunction getAnchorType(model) {\n  const range = typeof model.range === 'string' ? colCache.decode(model.range) : model.range;\n\n  return range.br ? 'xdr:twoCellAnchor' : 'xdr:oneCellAnchor';\n}\n\nclass DrawingXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'xdr:twoCellAnchor': new TwoCellAnchorXform(),\n      'xdr:oneCellAnchor': new OneCellAnchorXform(),\n    };\n  }\n\n  prepare(model) {\n    model.anchors.forEach((item, index) => {\n      item.anchorType = getAnchorType(item);\n      const anchor = this.map[item.anchorType];\n      anchor.prepare(item, {index});\n    });\n  }\n\n  get tag() {\n    return 'xdr:wsDr';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n    xmlStream.openNode(this.tag, DrawingXform.DRAWING_ATTRIBUTES);\n\n    model.anchors.forEach(item => {\n      const anchor = this.map[item.anchorType];\n      anchor.render(xmlStream, item);\n    });\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case this.tag:\n        this.reset();\n        this.model = {\n          anchors: [],\n        };\n        break;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model.anchors.push(this.parser.model);\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        return false;\n      default:\n        // could be some unrecognised tags\n        return true;\n    }\n  }\n\n  reconcile(model, options) {\n    model.anchors.forEach(anchor => {\n      if (anchor.br) {\n        this.map['xdr:twoCellAnchor'].reconcile(anchor, options);\n      } else {\n        this.map['xdr:oneCellAnchor'].reconcile(anchor, options);\n      }\n    });\n  }\n}\n\nDrawingXform.DRAWING_ATTRIBUTES = {\n  'xmlns:xdr': 'http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing',\n  'xmlns:a': 'http://schemas.openxmlformats.org/drawingml/2006/main',\n};\n\nmodule.exports = DrawingXform;\n"], "mappings": ";;AAAA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,MAAMC,SAAS,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AAEtD,MAAME,SAAS,GAAGF,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMG,kBAAkB,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AAC7D,MAAMI,kBAAkB,GAAGJ,OAAO,CAAC,yBAAyB,CAAC;AAE7D,SAASK,aAAaA,CAACC,KAAK,EAAE;EAC5B,MAAMC,KAAK,GAAG,OAAOD,KAAK,CAACC,KAAK,KAAK,QAAQ,GAAGR,QAAQ,CAACS,MAAM,CAACF,KAAK,CAACC,KAAK,CAAC,GAAGD,KAAK,CAACC,KAAK;EAE1F,OAAOA,KAAK,CAACE,EAAE,GAAG,mBAAmB,GAAG,mBAAmB;AAC7D;AAEA,MAAMC,YAAY,SAASR,SAAS,CAAC;EACnCS,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,mBAAmB,EAAE,IAAIT,kBAAkB,CAAC,CAAC;MAC7C,mBAAmB,EAAE,IAAIC,kBAAkB,CAAC;IAC9C,CAAC;EACH;EAEAS,OAAOA,CAACP,KAAK,EAAE;IACbA,KAAK,CAACQ,OAAO,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MACrCD,IAAI,CAACE,UAAU,GAAGb,aAAa,CAACW,IAAI,CAAC;MACrC,MAAMG,MAAM,GAAG,IAAI,CAACP,GAAG,CAACI,IAAI,CAACE,UAAU,CAAC;MACxCC,MAAM,CAACN,OAAO,CAACG,IAAI,EAAE;QAACC;MAAK,CAAC,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEA,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,UAAU;EACnB;EAEAC,MAAMA,CAACC,SAAS,EAAEhB,KAAK,EAAE;IACvBgB,SAAS,CAACC,OAAO,CAACtB,SAAS,CAACuB,gBAAgB,CAAC;IAC7CF,SAAS,CAACG,QAAQ,CAAC,IAAI,CAACL,GAAG,EAAEV,YAAY,CAACgB,kBAAkB,CAAC;IAE7DpB,KAAK,CAACQ,OAAO,CAACC,OAAO,CAACC,IAAI,IAAI;MAC5B,MAAMG,MAAM,GAAG,IAAI,CAACP,GAAG,CAACI,IAAI,CAACE,UAAU,CAAC;MACxCC,MAAM,CAACE,MAAM,CAACC,SAAS,EAAEN,IAAI,CAAC;IAChC,CAAC,CAAC;IAEFM,SAAS,CAACK,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACX,GAAG;QACX,IAAI,CAACY,KAAK,CAAC,CAAC;QACZ,IAAI,CAAC1B,KAAK,GAAG;UACXQ,OAAO,EAAE;QACX,CAAC;QACD;MACF;QACE,IAAI,CAACgB,MAAM,GAAG,IAAI,CAAClB,GAAG,CAACiB,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAI,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACJ,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACG,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACJ,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACJ,IAAI,CAAC,EAAE;QACjC,IAAI,CAACzB,KAAK,CAACQ,OAAO,CAACsB,IAAI,CAAC,IAAI,CAACN,MAAM,CAACxB,KAAK,CAAC;QAC1C,IAAI,CAACwB,MAAM,GAAGO,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQN,IAAI;MACV,KAAK,IAAI,CAACX,GAAG;QACX,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;EAEAkB,SAASA,CAAChC,KAAK,EAAEiC,OAAO,EAAE;IACxBjC,KAAK,CAACQ,OAAO,CAACC,OAAO,CAACI,MAAM,IAAI;MAC9B,IAAIA,MAAM,CAACV,EAAE,EAAE;QACb,IAAI,CAACG,GAAG,CAAC,mBAAmB,CAAC,CAAC0B,SAAS,CAACnB,MAAM,EAAEoB,OAAO,CAAC;MAC1D,CAAC,MAAM;QACL,IAAI,CAAC3B,GAAG,CAAC,mBAAmB,CAAC,CAAC0B,SAAS,CAACnB,MAAM,EAAEoB,OAAO,CAAC;MAC1D;IACF,CAAC,CAAC;EACJ;AACF;AAEA7B,YAAY,CAACgB,kBAAkB,GAAG;EAChC,WAAW,EAAE,qEAAqE;EAClF,SAAS,EAAE;AACb,CAAC;AAEDc,MAAM,CAACC,OAAO,GAAG/B,YAAY"}